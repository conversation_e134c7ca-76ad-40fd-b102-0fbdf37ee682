"""
Real Audio Stream Listener - Listens to desktop audio and responds to speech
"""
import sys
import os
import logging
import time
import threading
import queue
import numpy as np
import sounddevice as sd
import requests
import json
from pathlib import Path
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QTextEdit, QLabel, QProgressBar
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QThread, pyqtSlot

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AudioProcessor(QThread):
    """Processes audio and detects speech"""
    speech_detected = pyqtSignal(str)  # Transcribed text
    audio_level_updated = pyqtSignal(float)  # Audio level
    error_occurred = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.is_listening = False
        self.audio_queue = queue.Queue()
        self.sample_rate = 44100
        self.chunk_size = 1024
        self.speech_threshold = 0.01
        self.silence_duration = 2.0
        self.last_speech_time = 0
        self.audio_buffer = []
        
    def start_listening(self):
        """Start listening to system audio"""
        self.is_listening = True
        self.start()
    
    def stop_listening(self):
        """Stop listening"""
        self.is_listening = False
        self.wait()
    
    def audio_callback(self, indata, frames, time, status):
        """Audio input callback"""
        if status:
            logger.warning(f"Audio callback status: {status}")
        
        # Convert to mono
        if indata.shape[1] > 1:
            audio_data = np.mean(indata, axis=1)
        else:
            audio_data = indata[:, 0]
        
        # Calculate audio level
        rms = np.sqrt(np.mean(audio_data ** 2))
        self.audio_level_updated.emit(float(rms))
        
        # Check for speech
        if rms > self.speech_threshold:
            self.last_speech_time = time.currentTime
            self.audio_buffer.extend(audio_data)
        
        # Check for silence (end of speech)
        current_time = time.currentTime
        if (self.last_speech_time > 0 and 
            current_time - self.last_speech_time > self.silence_duration and
            len(self.audio_buffer) > 0):
            
            # Process accumulated audio
            self.process_audio_buffer()
            self.audio_buffer = []
            self.last_speech_time = 0
    
    def process_audio_buffer(self):
        """Process accumulated audio buffer"""
        try:
            if len(self.audio_buffer) < self.sample_rate:  # Less than 1 second
                return
            
            # Convert to numpy array
            audio_array = np.array(self.audio_buffer, dtype=np.float32)
            
            # Try to transcribe using available methods
            text = self.transcribe_audio(audio_array)
            
            if text and len(text.strip()) > 3:
                self.speech_detected.emit(text.strip())
                
        except Exception as e:
            logger.error(f"Error processing audio buffer: {e}")
            self.error_occurred.emit(f"Audio processing error: {e}")
    
    def transcribe_audio(self, audio_data):
        """Transcribe audio using available methods"""
        # Method 1: Try Google Speech Recognition (free, online)
        try:
            import speech_recognition as sr
            
            # Convert numpy array to audio data
            audio_bytes = (audio_data * 32767).astype(np.int16).tobytes()
            
            # Create recognizer
            r = sr.Recognizer()
            
            # Create audio data object
            audio_data_obj = sr.AudioData(audio_bytes, self.sample_rate, 2)
            
            # Recognize speech
            text = r.recognize_google(audio_data_obj)
            logger.info(f"Google Speech Recognition: {text}")
            return text
            
        except ImportError:
            logger.warning("SpeechRecognition not available")
        except Exception as e:
            logger.warning(f"Google Speech Recognition failed: {e}")
        
        # Method 2: Simple keyword detection (fallback)
        return self.simple_speech_detection(audio_data)
    
    def simple_speech_detection(self, audio_data):
        """Simple speech detection based on audio characteristics"""
        # This is a very basic fallback - just detects that speech-like audio occurred
        rms = np.sqrt(np.mean(audio_data ** 2))
        
        if rms > self.speech_threshold * 2:  # Higher threshold for "speech"
            return "[Speech detected - install SpeechRecognition for transcription]"
        
        return None
    
    def run(self):
        """Main audio processing loop"""
        try:
            # Find system audio device
            devices = sd.query_devices()
            system_device = None
            
            for i, device in enumerate(devices):
                device_name = device['name'].lower()
                if any(keyword in device_name for keyword in ['stereo mix', 'loopback', 'what u hear', 'wave out mix']):
                    system_device = i
                    break
            
            if system_device is None:
                self.error_occurred.emit("No system audio device found. Enable 'Stereo Mix' in Windows sound settings.")
                return
            
            logger.info(f"Using audio device: {devices[system_device]['name']}")
            
            # Start audio stream
            with sd.InputStream(
                device=system_device,
                channels=2,
                samplerate=self.sample_rate,
                blocksize=self.chunk_size,
                callback=self.audio_callback,
                dtype=np.float32
            ):
                logger.info("Audio stream started")
                
                while self.is_listening:
                    time.sleep(0.1)
                
                logger.info("Audio stream stopped")
                
        except Exception as e:
            logger.error(f"Audio processing error: {e}")
            self.error_occurred.emit(f"Audio error: {e}")

class OllamaAI(QThread):
    """Ollama AI processor"""
    response_ready = pyqtSignal(str, str)  # input_text, ai_response
    error_occurred = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.model_name = "llama3.2:1b"
        self.request_queue = queue.Queue()
        self.running = True
    
    def add_request(self, text):
        """Add text for AI processing"""
        self.request_queue.put(text)
    
    def run(self):
        """Main AI processing loop"""
        while self.running:
            try:
                # Get request from queue (with timeout)
                text = self.request_queue.get(timeout=1.0)
                self.process_text(text)
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"AI processing error: {e}")
    
    def process_text(self, text):
        """Process text with AI"""
        try:
            # Check if Ollama is available
            response = requests.get("http://localhost:11434/api/tags", timeout=3)
            if response.status_code != 200:
                self.error_occurred.emit("Ollama not available")
                return
            
            # Generate AI response
            ai_response = requests.post(
                "http://localhost:11434/api/generate",
                json={
                    "model": self.model_name,
                    "prompt": f"Someone just said: '{text}'. Respond naturally and conversationally as if you're listening to their conversation. Keep it brief (1-2 sentences).",
                    "stream": False,
                    "options": {
                        "temperature": 0.8,
                        "max_tokens": 80
                    }
                },
                timeout=30
            )
            
            if ai_response.status_code == 200:
                data = ai_response.json()
                response_text = data.get("response", "").strip()
                
                # Clean response
                if response_text.startswith("Assistant:"):
                    response_text = response_text[10:].strip()
                
                self.response_ready.emit(text, response_text)
            else:
                self.error_occurred.emit(f"Ollama API error: {ai_response.status_code}")
                
        except Exception as e:
            logger.error(f"AI processing error: {e}")
            self.error_occurred.emit(f"AI error: {e}")
    
    def stop(self):
        """Stop AI processing"""
        self.running = False

class RealAudioListenerWindow(QMainWindow):
    """Main window for real audio listening"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🎧 Real Audio Stream Listener - AI Responds to Desktop Audio")
        self.setGeometry(100, 100, 900, 700)
        
        # Components
        self.audio_processor = None
        self.ollama_ai = None
        self.tts_engine = None
        self.is_listening = False
        
        self.setup_ui()
        self.setup_components()
    
    def setup_ui(self):
        """Setup user interface"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Title
        title = QLabel("🎧 Real Audio Stream Listener")
        title.setStyleSheet("font-size: 20px; font-weight: bold; padding: 15px; text-align: center; background-color: #2196F3; color: white; border-radius: 10px;")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # Status and controls
        controls_layout = QHBoxLayout()
        
        self.listen_button = QPushButton("🎤 Start Listening to Desktop Audio")
        self.listen_button.setMinimumHeight(50)
        self.listen_button.setStyleSheet("""
            QPushButton {
                font-size: 14px; font-weight: bold; background-color: #4CAF50; color: white; 
                border: none; border-radius: 8px; padding: 10px;
            }
            QPushButton:hover { background-color: #45a049; }
        """)
        self.listen_button.clicked.connect(self.toggle_listening)
        controls_layout.addWidget(self.listen_button)
        
        self.status_label = QLabel("Status: Ready")
        self.status_label.setStyleSheet("padding: 10px; background-color: #f0f0f0; border-radius: 5px; font-weight: bold;")
        controls_layout.addWidget(self.status_label)
        
        layout.addLayout(controls_layout)
        
        # Audio level indicator
        audio_layout = QHBoxLayout()
        audio_layout.addWidget(QLabel("Audio Level:"))
        self.audio_level_bar = QProgressBar()
        self.audio_level_bar.setRange(0, 100)
        self.audio_level_bar.setValue(0)
        self.audio_level_bar.setStyleSheet("QProgressBar::chunk { background-color: #4CAF50; }")
        audio_layout.addWidget(self.audio_level_bar)
        layout.addLayout(audio_layout)
        
        # Conversation area
        self.conversation_area = QTextEdit()
        self.conversation_area.setReadOnly(True)
        self.conversation_area.setStyleSheet("""
            QTextEdit {
                font-size: 12px; padding: 15px; background-color: #fafafa;
                border: 2px solid #ddd; border-radius: 10px;
            }
        """)
        layout.addWidget(self.conversation_area)
        
        # Info
        info = QLabel("💡 This app listens to your desktop audio (YouTube, music, etc.) and responds to speech it hears!")
        info.setStyleSheet("padding: 10px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; color: #856404;")
        info.setWordWrap(True)
        layout.addWidget(info)
    
    def setup_components(self):
        """Setup audio and AI components"""
        # Setup TTS
        try:
            import pyttsx3
            self.tts_engine = pyttsx3.init()
        except Exception as e:
            logger.error(f"TTS setup failed: {e}")
        
        # Setup audio processor
        self.audio_processor = AudioProcessor()
        self.audio_processor.speech_detected.connect(self.on_speech_detected)
        self.audio_processor.audio_level_updated.connect(self.on_audio_level_updated)
        self.audio_processor.error_occurred.connect(self.on_error)
        
        # Setup Ollama AI
        self.ollama_ai = OllamaAI()
        self.ollama_ai.response_ready.connect(self.on_ai_response)
        self.ollama_ai.error_occurred.connect(self.on_error)
        self.ollama_ai.start()
    
    def toggle_listening(self):
        """Toggle audio listening"""
        if self.is_listening:
            self.stop_listening()
        else:
            self.start_listening()
    
    def start_listening(self):
        """Start listening to audio"""
        try:
            self.audio_processor.start_listening()
            self.is_listening = True
            self.listen_button.setText("🛑 Stop Listening")
            self.listen_button.setStyleSheet("""
                QPushButton {
                    font-size: 14px; font-weight: bold; background-color: #f44336; color: white; 
                    border: none; border-radius: 8px; padding: 10px;
                }
                QPushButton:hover { background-color: #da190b; }
            """)
            self.status_label.setText("Status: 🎧 Listening to desktop audio...")
            self.add_message("System", "🎧 Started listening to desktop audio streams!", "#4CAF50")
            
        except Exception as e:
            self.on_error(f"Failed to start listening: {e}")
    
    def stop_listening(self):
        """Stop listening to audio"""
        try:
            self.audio_processor.stop_listening()
            self.is_listening = False
            self.listen_button.setText("🎤 Start Listening to Desktop Audio")
            self.listen_button.setStyleSheet("""
                QPushButton {
                    font-size: 14px; font-weight: bold; background-color: #4CAF50; color: white; 
                    border: none; border-radius: 8px; padding: 10px;
                }
                QPushButton:hover { background-color: #45a049; }
            """)
            self.status_label.setText("Status: Ready")
            self.add_message("System", "🛑 Stopped listening to audio", "#FF5722")
            
        except Exception as e:
            self.on_error(f"Failed to stop listening: {e}")
    
    @pyqtSlot(str)
    def on_speech_detected(self, text):
        """Handle detected speech"""
        self.add_message("🎧 Heard", text, "#2196F3")
        
        # Send to AI for response
        if self.ollama_ai:
            self.ollama_ai.add_request(text)
    
    @pyqtSlot(str, str)
    def on_ai_response(self, input_text, ai_response):
        """Handle AI response"""
        self.add_message("🤖 AI", ai_response, "#4CAF50")
        
        # Speak response
        if self.tts_engine:
            try:
                self.tts_engine.say(ai_response)
                self.tts_engine.runAndWait()
            except Exception as e:
                logger.error(f"TTS error: {e}")
    
    @pyqtSlot(float)
    def on_audio_level_updated(self, level):
        """Update audio level indicator"""
        self.audio_level_bar.setValue(int(level * 1000))  # Scale up for visibility
    
    @pyqtSlot(str)
    def on_error(self, error_message):
        """Handle errors"""
        self.add_message("❌ Error", error_message, "#F44336")
        self.status_label.setText(f"Status: Error - {error_message[:30]}...")
    
    def add_message(self, sender: str, message: str, color: str):
        """Add message to conversation"""
        timestamp = time.strftime("%H:%M:%S")
        
        formatted_message = f"""
        <div style="margin: 8px 0; padding: 12px; border-left: 4px solid {color}; background-color: #f9f9f9; border-radius: 8px;">
            <strong style="color: {color};">[{timestamp}] {sender}:</strong><br>
            <span style="margin-left: 10px; color: #333;">{message}</span>
        </div>
        """
        
        self.conversation_area.append(formatted_message)
        
        # Auto-scroll
        cursor = self.conversation_area.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.conversation_area.setTextCursor(cursor)
    
    def closeEvent(self, event):
        """Handle window close"""
        if self.is_listening:
            self.stop_listening()
        
        if self.ollama_ai:
            self.ollama_ai.stop()
            self.ollama_ai.wait()
        
        event.accept()

def main():
    """Main entry point"""
    app = QApplication(sys.argv)
    
    window = RealAudioListenerWindow()
    window.show()
    
    # Welcome message
    window.add_message("System", "🎉 Welcome to Real Audio Stream Listener!", "#4CAF50")
    window.add_message("System", "This app listens to your desktop audio and responds to speech it hears.", "#2196F3")
    window.add_message("System", "Make sure 'Stereo Mix' is enabled in Windows sound settings.", "#FF9800")
    window.add_message("System", "Click 'Start Listening' and play some YouTube videos or music!", "#9C27B0")
    
    return app.exec()

if __name__ == "__main__":
    print("🎧 Starting Real Audio Stream Listener...")
    sys.exit(main())
