"""
Configuration settings for the Human-like Audio AI Desktop Application
"""
import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    # Application Settings
    APP_NAME = "Human-like Audio AI"
    VERSION = "1.0.0"
    
    # Audio Settings
    SAMPLE_RATE = 44100
    CHANNELS = 2
    CHUNK_SIZE = 1024
    AUDIO_FORMAT = 'float32'
    BUFFER_DURATION = 2.0  # seconds
    
    # Speech Recognition Settings
    WHISPER_MODEL = "base"  # base, small, medium, large
    SPEECH_THRESHOLD = 0.01  # Minimum audio level to consider as speech
    SILENCE_DURATION = 2.0  # seconds of silence before processing
    
    # AI Engine Settings
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
    GPT_MODEL = "gpt-3.5-turbo"
    MAX_TOKENS = 150
    TEMPERATURE = 0.8
    
    # Learning System Settings
    LEARNING_ENABLED = True
    FEEDBACK_THRESHOLD = 0.7
    MAX_CONTEXT_HISTORY = 10
    
    # Text-to-Speech Settings
    TTS_ENABLED = True
    TTS_RATE = 200
    TTS_VOLUME = 0.9
    
    # Database Settings
    DATABASE_PATH = Path("data/conversations.db")
    
    # UI Settings
    WINDOW_WIDTH = 800
    WINDOW_HEIGHT = 600
    THEME = "dark"
    
    # Logging Settings
    LOG_LEVEL = "INFO"
    LOG_FILE = "logs/app.log"
    
    # Directories
    DATA_DIR = Path("data")
    LOGS_DIR = Path("logs")
    MODELS_DIR = Path("models")
    
    @classmethod
    def ensure_directories(cls):
        """Create necessary directories if they don't exist"""
        cls.DATA_DIR.mkdir(exist_ok=True)
        cls.LOGS_DIR.mkdir(exist_ok=True)
        cls.MODELS_DIR.mkdir(exist_ok=True)
    
    @classmethod
    def validate_config(cls):
        """Validate configuration settings"""
        if not cls.OPENAI_API_KEY:
            raise ValueError("OPENAI_API_KEY environment variable is required")
        
        return True

# Personality and Response Settings
class PersonalityConfig:
    # Human-like traits
    EMPATHY_LEVEL = 0.8
    HUMOR_LEVEL = 0.6
    CURIOSITY_LEVEL = 0.7
    
    # Response styles
    RESPONSE_STYLES = [
        "empathetic",
        "humorous", 
        "analytical",
        "supportive",
        "curious"
    ]
    
    # Emotional responses
    EMOTION_MAPPING = {
        "happy": "I'm so glad to hear that! 😊",
        "sad": "I can sense you might be feeling down. I'm here if you need to talk.",
        "angry": "It sounds like something is really bothering you. Want to share what's on your mind?",
        "excited": "Your enthusiasm is contagious! Tell me more!",
        "confused": "I can help clarify things if you'd like to discuss it.",
        "neutral": "I'm listening and here to chat about whatever interests you."
    }
