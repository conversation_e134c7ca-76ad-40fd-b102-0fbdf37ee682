"""
Main application for Human-like Audio AI Desktop Application
"""
import sys
import os
import logging
import time
import threading
from pathlib import Path
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import QObject, pyqtSignal, QThread, pyqtSlot

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import application modules
from config import Config
from utils import setup_logging
from database import DatabaseManager
from audio_capture import SystemAudioCapture
from speech_recognition import WhisperSpeechRecognizer
from nlp_processor import NLPProcessor
from ai_engine import AIResponseEngine
from tts_engine import TextToSpeechEngine
from learning_system import LearningSystem
from ui.main_window import MainWindow

logger = logging.getLogger(__name__)

class AudioProcessingWorker(QObject):
    """Worker for processing audio in separate thread"""
    
    # Signals
    transcription_ready = pyqtSignal(str, dict)  # text, analysis
    response_ready = pyqtSignal(str, str, str)   # user_text, ai_response, emotion
    error_occurred = pyqtSignal(str)
    audio_level_updated = pyqtSignal(float)
    
    def __init__(self, app_controller):
        super().__init__()
        self.app_controller = app_controller
        self.is_processing = False
    
    @pyqtSlot(object, dict, float)
    def process_audio(self, audio_data, features, timestamp):
        """Process captured audio"""
        try:
            if self.is_processing:
                return
            
            self.is_processing = True
            
            # Update audio level
            audio_level = features.get('rms_energy', 0.0)
            self.audio_level_updated.emit(audio_level)
            
            # Check if audio contains speech
            if not self.app_controller.speech_recognizer.is_speech_detected(audio_data, Config.SAMPLE_RATE):
                return
            
            # Transcribe audio
            transcription_result = self.app_controller.speech_recognizer.transcribe_audio(
                audio_data, Config.SAMPLE_RATE
            )
            
            text = transcription_result.get("text", "").strip()
            if not text:
                return
            
            # Analyze text
            analysis = self.app_controller.nlp_processor.analyze_text_comprehensive(text)
            
            # Emit transcription ready signal
            self.transcription_ready.emit(text, analysis)
            
        except Exception as e:
            logger.error(f"Error processing audio: {e}")
            self.error_occurred.emit(str(e))
        finally:
            self.is_processing = False
    
    @pyqtSlot(str, dict)
    def generate_response(self, user_text, analysis):
        """Generate AI response"""
        try:
            # Get learning recommendations
            context = {"analysis": analysis}
            recommendations = self.app_controller.learning_system.get_response_recommendation(user_text, context)
            context.update(recommendations)
            
            # Generate response
            response_result = self.app_controller.ai_engine.generate_response(
                user_text, context, self.app_controller.get_conversation_history()
            )
            
            ai_response = response_result.get("response", "")
            emotion = analysis.get("emotion", {}).get("emotion", "neutral")
            
            # Record interaction for learning
            self.app_controller.learning_system.record_interaction(
                user_text, ai_response, context
            )
            
            # Emit response ready signal
            self.response_ready.emit(user_text, ai_response, emotion)
            
        except Exception as e:
            logger.error(f"Error generating response: {e}")
            self.error_occurred.emit(str(e))

class HumanLikeAudioAI(QObject):
    """Main application controller"""
    
    # Signals for worker communication
    audio_captured = pyqtSignal(object, dict, float)
    transcription_received = pyqtSignal(str, dict)
    
    def __init__(self):
        super().__init__()
        
        # Initialize components
        self.db_manager = None
        self.audio_capture = None
        self.speech_recognizer = None
        self.nlp_processor = None
        self.ai_engine = None
        self.tts_engine = None
        self.learning_system = None
        self.main_window = None
        
        # Worker thread
        self.worker_thread = None
        self.audio_worker = None
        
        # Application state
        self.is_running = False
        self.session_start_time = None
        
        logger.info("Application controller initialized")
    
    def initialize_components(self):
        """Initialize all application components"""
        try:
            logger.info("Initializing application components...")
            
            # Ensure directories exist
            Config.ensure_directories()
            
            # Initialize database
            self.db_manager = DatabaseManager()
            logger.info("Database manager initialized")
            
            # Initialize audio capture
            self.audio_capture = SystemAudioCapture()
            self.audio_capture.set_audio_callback(self.on_audio_captured)
            logger.info("Audio capture initialized")
            
            # Initialize speech recognition
            self.speech_recognizer = WhisperSpeechRecognizer()
            logger.info("Speech recognizer initialized")
            
            # Initialize NLP processor
            self.nlp_processor = NLPProcessor()
            logger.info("NLP processor initialized")
            
            # Initialize AI engine
            self.ai_engine = AIResponseEngine()
            logger.info("AI engine initialized")
            
            # Initialize TTS engine
            self.tts_engine = TextToSpeechEngine()
            logger.info("TTS engine initialized")
            
            # Initialize learning system
            self.learning_system = LearningSystem(self.db_manager)
            self.learning_system.load_models()
            logger.info("Learning system initialized")
            
            # Setup worker thread
            self.setup_worker_thread()
            
            logger.info("All components initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing components: {e}")
            return False
    
    def setup_worker_thread(self):
        """Setup worker thread for audio processing"""
        try:
            self.worker_thread = QThread()
            self.audio_worker = AudioProcessingWorker(self)
            self.audio_worker.moveToThread(self.worker_thread)
            
            # Connect signals
            self.audio_captured.connect(self.audio_worker.process_audio)
            self.transcription_received.connect(self.audio_worker.generate_response)
            
            self.audio_worker.transcription_ready.connect(self.on_transcription_ready)
            self.audio_worker.response_ready.connect(self.on_response_ready)
            self.audio_worker.error_occurred.connect(self.on_error)
            self.audio_worker.audio_level_updated.connect(self.on_audio_level_updated)
            
            self.worker_thread.start()
            logger.info("Worker thread setup completed")
            
        except Exception as e:
            logger.error(f"Error setting up worker thread: {e}")
    
    def start_listening(self):
        """Start listening to audio"""
        try:
            if self.is_running:
                logger.warning("Already listening")
                return
            
            # Load models if not already loaded
            if not self.speech_recognizer.model:
                self.main_window.show_info("Loading speech recognition model...")
                if not self.speech_recognizer.load_model():
                    raise Exception("Failed to load speech recognition model")
            
            if not self.nlp_processor.sentiment_analyzer:
                self.main_window.show_info("Loading NLP models...")
                self.nlp_processor.load_models()
            
            if not self.tts_engine.is_initialized:
                self.main_window.show_info("Initializing text-to-speech...")
                self.tts_engine.initialize_engine()
            
            # Start audio capture
            self.audio_capture.start_capture()
            
            self.is_running = True
            self.session_start_time = time.time()
            self.main_window.set_session_start_time(self.session_start_time)
            
            self.main_window.show_info("Started listening to system audio")
            logger.info("Started listening")
            
        except Exception as e:
            logger.error(f"Error starting listening: {e}")
            self.main_window.show_error(f"Failed to start listening: {e}")
    
    def stop_listening(self):
        """Stop listening to audio"""
        try:
            if not self.is_running:
                logger.warning("Not currently listening")
                return
            
            # Stop audio capture
            self.audio_capture.stop_capture()
            
            self.is_running = False
            self.session_start_time = None
            
            self.main_window.show_info("Stopped listening")
            logger.info("Stopped listening")
            
        except Exception as e:
            logger.error(f"Error stopping listening: {e}")
    
    def on_audio_captured(self, audio_data, features, timestamp):
        """Handle captured audio"""
        try:
            # Emit signal to worker thread
            self.audio_captured.emit(audio_data, features, timestamp)
            
        except Exception as e:
            logger.error(f"Error handling captured audio: {e}")
    
    @pyqtSlot(str, dict)
    def on_transcription_ready(self, text, analysis):
        """Handle transcription ready"""
        try:
            logger.info(f"Transcribed: {text}")
            
            # Update NLP context
            self.nlp_processor.update_context(text, analysis)
            
            # Emit signal to generate response
            self.transcription_received.emit(text, analysis)
            
        except Exception as e:
            logger.error(f"Error handling transcription: {e}")
    
    @pyqtSlot(str, str, str)
    def on_response_ready(self, user_text, ai_response, emotion):
        """Handle AI response ready"""
        try:
            logger.info(f"AI Response: {ai_response}")
            
            # Add to conversation display
            self.main_window.add_conversation_entry(user_text, ai_response, emotion)
            
            # Speak response if TTS is enabled
            settings = self.main_window.get_settings()
            if settings.get("tts_enabled", True):
                self.tts_engine.speak(ai_response, emotion)
            
            # Update learning insights
            insights = self.learning_system.get_learning_insights()
            self.main_window.update_learning_insights(insights)
            
        except Exception as e:
            logger.error(f"Error handling response: {e}")
    
    @pyqtSlot(str)
    def on_error(self, error_message):
        """Handle error from worker"""
        logger.error(f"Worker error: {error_message}")
        self.main_window.show_error(error_message)
    
    @pyqtSlot(float)
    def on_audio_level_updated(self, level):
        """Handle audio level update"""
        self.main_window.set_audio_level(level)
    
    def get_conversation_history(self):
        """Get conversation history for context"""
        try:
            return self.main_window.conversation_widget.get_last_messages(5)
        except:
            return []
    
    def shutdown(self):
        """Shutdown application"""
        try:
            logger.info("Shutting down application...")
            
            # Stop listening
            if self.is_running:
                self.stop_listening()
            
            # Train learning models with final data
            if self.learning_system:
                self.learning_system.train_models()
            
            # Shutdown components
            if self.tts_engine:
                self.tts_engine.shutdown()
            
            if self.worker_thread:
                self.worker_thread.quit()
                self.worker_thread.wait(3000)
            
            logger.info("Application shutdown completed")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")

def main():
    """Main application entry point"""
    try:
        # Setup logging
        setup_logging(Config.LOG_LEVEL, Config.LOG_FILE)
        logger.info(f"Starting {Config.APP_NAME} v{Config.VERSION}")
        
        # Validate configuration
        try:
            Config.validate_config()
        except ValueError as e:
            print(f"Configuration error: {e}")
            print("Please set the OPENAI_API_KEY environment variable")
            return 1
        
        # Create QApplication
        app = QApplication(sys.argv)
        app.setApplicationName(Config.APP_NAME)
        app.setApplicationVersion(Config.VERSION)
        
        # Create main application controller
        audio_ai = HumanLikeAudioAI()
        
        # Initialize components
        if not audio_ai.initialize_components():
            QMessageBox.critical(None, "Initialization Error", 
                               "Failed to initialize application components. Check logs for details.")
            return 1
        
        # Create and show main window
        audio_ai.main_window = MainWindow()
        
        # Connect UI signals
        audio_ai.main_window.start_listening_signal.connect(audio_ai.start_listening)
        audio_ai.main_window.stop_listening_signal.connect(audio_ai.stop_listening)
        
        audio_ai.main_window.show()
        
        # Setup shutdown handler
        app.aboutToQuit.connect(audio_ai.shutdown)
        
        logger.info("Application started successfully")
        
        # Run application
        return app.exec()
        
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        if 'app' in locals():
            QMessageBox.critical(None, "Fatal Error", f"A fatal error occurred: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
