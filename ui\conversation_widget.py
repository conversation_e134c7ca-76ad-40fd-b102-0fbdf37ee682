"""
Conversation widget for displaying chat messages
"""
import logging
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QScrollArea, 
                            QLabel, QFrame, QTextEdit, QSizePolicy)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QFont, QPalette, QColor
from typing import List, Dict, Any
import datetime

logger = logging.getLogger(__name__)

class MessageWidget(QFrame):
    """Individual message widget"""
    
    def __init__(self, sender: str, message: str, emotion: str = "neutral", timestamp: str = None):
        super().__init__()
        
        self.sender = sender
        self.message = message
        self.emotion = emotion
        self.timestamp = timestamp or datetime.datetime.now().strftime("%H:%M:%S")
        
        self.setup_ui()
    
    def setup_ui(self):
        """Setup message UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 5, 10, 5)
        
        # Header with sender and timestamp
        header_layout = QHBoxLayout()
        
        sender_label = QLabel(self.sender)
        sender_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        
        timestamp_label = QLabel(self.timestamp)
        timestamp_label.setFont(QFont("Arial", 8))
        timestamp_label.setStyleSheet("color: #666666;")
        
        header_layout.addWidget(sender_label)
        header_layout.addStretch()
        header_layout.addWidget(timestamp_label)
        
        layout.addLayout(header_layout)
        
        # Message content
        message_label = QLabel(self.message)
        message_label.setWordWrap(True)
        message_label.setFont(QFont("Arial", 10))
        message_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
        
        layout.addWidget(message_label)
        
        # Style based on sender and emotion
        self.apply_styling()
    
    def apply_styling(self):
        """Apply styling based on sender and emotion"""
        if self.sender == "User":
            # User message styling
            self.setStyleSheet("""
                QFrame {
                    background-color: #e3f2fd;
                    border: 1px solid #bbdefb;
                    border-radius: 10px;
                    margin: 2px 50px 2px 2px;
                }
            """)
        else:
            # AI message styling with emotion-based colors
            emotion_colors = {
                "happy": "#e8f5e8",
                "sad": "#fff3e0", 
                "excited": "#f3e5f5",
                "angry": "#ffebee",
                "calm": "#e0f2f1",
                "neutral": "#f5f5f5"
            }
            
            bg_color = emotion_colors.get(self.emotion, "#f5f5f5")
            
            self.setStyleSheet(f"""
                QFrame {{
                    background-color: {bg_color};
                    border: 1px solid #e0e0e0;
                    border-radius: 10px;
                    margin: 2px 2px 2px 50px;
                }}
            """)

class ConversationWidget(QWidget):
    """Widget for displaying conversation history"""
    
    def __init__(self):
        super().__init__()
        
        self.messages: List[MessageWidget] = []
        self.max_messages = 100  # Limit to prevent memory issues
        
        self.setup_ui()
        
        logger.info("Conversation widget initialized")
    
    def setup_ui(self):
        """Setup conversation UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # Scroll area for messages
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        
        # Container widget for messages
        self.messages_container = QWidget()
        self.messages_layout = QVBoxLayout(self.messages_container)
        self.messages_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.messages_layout.setSpacing(5)
        
        # Add stretch to push messages to top
        self.messages_layout.addStretch()
        
        self.scroll_area.setWidget(self.messages_container)
        layout.addWidget(self.scroll_area)
        
        # Welcome message
        self.add_welcome_message()
    
    def add_welcome_message(self):
        """Add welcome message"""
        welcome_text = ("Hello! I'm your AI assistant. I'm listening to your environment "
                       "and ready to have natural conversations with you. Start by clicking "
                       "'Start Listening' and I'll respond to what I hear!")
        
        self.add_message("AI", welcome_text, "neutral")
    
    def add_message(self, sender: str, message: str, emotion: str = "neutral", timestamp: str = None):
        """Add a message to the conversation"""
        try:
            # Create message widget
            message_widget = MessageWidget(sender, message, emotion, timestamp)
            
            # Remove stretch before adding new message
            item_count = self.messages_layout.count()
            if item_count > 0:
                stretch_item = self.messages_layout.itemAt(item_count - 1)
                if stretch_item.spacerItem():
                    self.messages_layout.removeItem(stretch_item)
            
            # Add message to layout
            self.messages_layout.addWidget(message_widget)
            
            # Add stretch back to push messages to top
            self.messages_layout.addStretch()
            
            # Add to messages list
            self.messages.append(message_widget)
            
            # Limit number of messages
            if len(self.messages) > self.max_messages:
                old_message = self.messages.pop(0)
                old_message.deleteLater()
            
            # Auto-scroll to bottom
            QTimer.singleShot(100, self.scroll_to_bottom)
            
            logger.debug(f"Added message from {sender}: {message[:50]}...")
            
        except Exception as e:
            logger.error(f"Error adding message: {e}")
    
    def scroll_to_bottom(self):
        """Scroll to the bottom of the conversation"""
        try:
            scrollbar = self.scroll_area.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())
        except Exception as e:
            logger.error(f"Error scrolling to bottom: {e}")
    
    def clear_conversation(self):
        """Clear all messages"""
        try:
            # Remove all message widgets
            for message in self.messages:
                message.deleteLater()
            
            self.messages.clear()
            
            # Clear layout
            while self.messages_layout.count():
                child = self.messages_layout.takeAt(0)
                if child.widget():
                    child.widget().deleteLater()
            
            # Add stretch back
            self.messages_layout.addStretch()
            
            # Add welcome message back
            self.add_welcome_message()
            
            logger.info("Conversation cleared")
            
        except Exception as e:
            logger.error(f"Error clearing conversation: {e}")
    
    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """Get conversation history as list of dictionaries"""
        try:
            history = []
            for message in self.messages:
                history.append({
                    "sender": message.sender,
                    "message": message.message,
                    "emotion": message.emotion,
                    "timestamp": message.timestamp
                })
            return history
            
        except Exception as e:
            logger.error(f"Error getting conversation history: {e}")
            return []
    
    def export_conversation(self, file_path: str):
        """Export conversation to text file"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(f"Conversation Export - {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("=" * 50 + "\n\n")
                
                for message in self.messages:
                    f.write(f"[{message.timestamp}] {message.sender}:\n")
                    f.write(f"{message.message}\n")
                    if message.emotion != "neutral":
                        f.write(f"(Emotion: {message.emotion})\n")
                    f.write("\n")
            
            logger.info(f"Conversation exported to {file_path}")
            
        except Exception as e:
            logger.error(f"Error exporting conversation: {e}")
    
    def search_messages(self, query: str) -> List[MessageWidget]:
        """Search for messages containing query"""
        try:
            query_lower = query.lower()
            matching_messages = []
            
            for message in self.messages:
                if query_lower in message.message.lower():
                    matching_messages.append(message)
            
            logger.debug(f"Found {len(matching_messages)} messages matching '{query}'")
            return matching_messages
            
        except Exception as e:
            logger.error(f"Error searching messages: {e}")
            return []
    
    def highlight_message(self, message_widget: MessageWidget):
        """Highlight a specific message"""
        try:
            # Temporarily change background color
            original_style = message_widget.styleSheet()
            
            highlight_style = original_style.replace(
                "background-color: #", "background-color: #ffeb3b; /* highlight */ background-color: #"
            )
            
            message_widget.setStyleSheet(highlight_style)
            
            # Scroll to message
            self.scroll_area.ensureWidgetVisible(message_widget)
            
            # Reset style after 2 seconds
            QTimer.singleShot(2000, lambda: message_widget.setStyleSheet(original_style))
            
        except Exception as e:
            logger.error(f"Error highlighting message: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get conversation statistics"""
        try:
            user_messages = sum(1 for msg in self.messages if msg.sender == "User")
            ai_messages = sum(1 for msg in self.messages if msg.sender == "AI")
            
            # Count emotions
            emotions = {}
            for msg in self.messages:
                if msg.sender == "AI" and msg.emotion != "neutral":
                    emotions[msg.emotion] = emotions.get(msg.emotion, 0) + 1
            
            # Calculate average message length
            total_chars = sum(len(msg.message) for msg in self.messages)
            avg_length = total_chars / len(self.messages) if self.messages else 0
            
            return {
                "total_messages": len(self.messages),
                "user_messages": user_messages,
                "ai_messages": ai_messages,
                "emotions_used": emotions,
                "average_message_length": avg_length
            }
            
        except Exception as e:
            logger.error(f"Error getting conversation statistics: {e}")
            return {}
    
    def set_max_messages(self, max_messages: int):
        """Set maximum number of messages to keep"""
        self.max_messages = max(10, max_messages)  # Minimum 10 messages
        
        # Remove excess messages if needed
        while len(self.messages) > self.max_messages:
            old_message = self.messages.pop(0)
            old_message.deleteLater()
        
        logger.info(f"Set max messages to {self.max_messages}")
    
    def get_last_messages(self, count: int) -> List[Dict[str, Any]]:
        """Get last N messages"""
        try:
            last_messages = self.messages[-count:] if count <= len(self.messages) else self.messages
            
            return [{
                "sender": msg.sender,
                "message": msg.message,
                "emotion": msg.emotion,
                "timestamp": msg.timestamp
            } for msg in last_messages]
            
        except Exception as e:
            logger.error(f"Error getting last messages: {e}")
            return []
