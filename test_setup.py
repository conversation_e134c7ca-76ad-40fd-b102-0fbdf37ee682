"""
Test script to check if all required packages are installed and working
"""
import sys
import importlib

def test_import(module_name, package_name=None):
    """Test if a module can be imported"""
    try:
        importlib.import_module(module_name)
        print(f"✅ {package_name or module_name} - OK")
        return True
    except ImportError as e:
        print(f"❌ {package_name or module_name} - MISSING: {e}")
        return False
    except Exception as e:
        print(f"⚠️  {package_name or module_name} - ERROR: {e}")
        return False

def main():
    print("🔍 Testing Audio AI Application Dependencies\n")
    
    # Core Python packages
    print("Core packages:")
    test_import("sys")
    test_import("os")
    test_import("threading")
    test_import("queue")
    test_import("json")
    test_import("logging")
    test_import("pathlib")
    test_import("datetime")
    test_import("time")
    
    print("\nScientific computing:")
    numpy_ok = test_import("numpy")
    scipy_ok = test_import("scipy")
    
    print("\nAudio processing:")
    sounddevice_ok = test_import("sounddevice")
    
    print("\nAI/ML packages:")
    openai_ok = test_import("openai")
    whisper_ok = test_import("whisper", "openai-whisper")
    torch_ok = test_import("torch", "PyTorch")
    transformers_ok = test_import("transformers", "Hugging Face Transformers")
    sklearn_ok = test_import("sklearn", "scikit-learn")
    
    print("\nUI packages:")
    pyqt6_ok = test_import("PyQt6", "PyQt6")
    
    print("\nTTS packages:")
    pyttsx3_ok = test_import("pyttsx3")
    
    print("\nUtility packages:")
    dotenv_ok = test_import("dotenv", "python-dotenv")
    requests_ok = test_import("requests")
    
    print("\nOptional packages:")
    test_import("matplotlib")
    test_import("seaborn")
    test_import("soundfile")
    test_import("librosa")
    
    # Summary
    print("\n" + "="*50)
    print("SUMMARY:")
    
    critical_packages = [
        ("NumPy", numpy_ok),
        ("SciPy", scipy_ok),
        ("SoundDevice", sounddevice_ok),
        ("OpenAI", openai_ok),
        ("Whisper", whisper_ok),
        ("PyQt6", pyqt6_ok),
        ("pyttsx3", pyttsx3_ok),
        ("python-dotenv", dotenv_ok)
    ]
    
    missing_critical = []
    for name, status in critical_packages:
        if not status:
            missing_critical.append(name)
    
    if not missing_critical:
        print("✅ All critical packages are installed!")
        print("\n🚀 You can now run the application with: python main.py")
        print("\n📝 Don't forget to:")
        print("   1. Add your OpenAI API key to the .env file")
        print("   2. Enable system audio capture (Stereo Mix on Windows)")
    else:
        print(f"❌ Missing critical packages: {', '.join(missing_critical)}")
        print("\n📦 Install missing packages with:")
        for package in missing_critical:
            if package == "Whisper":
                print(f"   pip install openai-whisper")
            elif package == "python-dotenv":
                print(f"   pip install python-dotenv")
            else:
                print(f"   pip install {package.lower()}")
    
    # Test basic functionality
    print("\n" + "="*50)
    print("BASIC FUNCTIONALITY TESTS:")
    
    if numpy_ok:
        try:
            import numpy as np
            arr = np.array([1, 2, 3])
            print(f"✅ NumPy array creation: {arr}")
        except Exception as e:
            print(f"❌ NumPy test failed: {e}")
    
    if sounddevice_ok:
        try:
            import sounddevice as sd
            devices = sd.query_devices()
            print(f"✅ SoundDevice found {len(devices)} audio devices")
        except Exception as e:
            print(f"❌ SoundDevice test failed: {e}")
    
    if pyqt6_ok:
        try:
            from PyQt6.QtWidgets import QApplication
            print("✅ PyQt6 widgets import successful")
        except Exception as e:
            print(f"❌ PyQt6 test failed: {e}")
    
    if pyttsx3_ok:
        try:
            import pyttsx3
            engine = pyttsx3.init()
            print("✅ pyttsx3 TTS engine initialization successful")
        except Exception as e:
            print(f"❌ pyttsx3 test failed: {e}")

if __name__ == "__main__":
    main()
