"""
AI Engine using Hugging Face Transformers (Free, Offline)
"""
import logging
import time
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)

class HuggingFaceAIEngine:
    """AI Engine using Hugging Face Transformers for free offline AI"""
    
    def __init__(self, model_name: str = "microsoft/DialoGPT-medium"):
        self.model_name = model_name
        self.tokenizer = None
        self.model = None
        self.conversation_history = []
        self.is_loaded = False
        
        # Personality traits
        self.personality_traits = {
            "empathy": 0.8,
            "humor": 0.6,
            "curiosity": 0.7
        }
        
        logger.info(f"HuggingFace AI Engine initialized with model: {model_name}")
    
    def load_model(self) -> bool:
        """Load the Hugging Face model"""
        try:
            logger.info(f"Loading model {self.model_name}...")
            
            # Try to import transformers
            try:
                from transformers import AutoTokenizer, AutoModelForCausalLM
                import torch
            except ImportError as e:
                logger.error(f"Transformers not available: {e}")
                return False
            
            # Load tokenizer and model
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            self.model = AutoModelForCausalLM.from_pretrained(self.model_name)
            
            # Add padding token if not present
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            self.is_loaded = True
            logger.info("Model loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            return False
    
    def generate_response(self, 
                         user_input: str, 
                         context: Dict[str, Any],
                         conversation_history: List[Dict] = None) -> Dict[str, Any]:
        """Generate AI response using Hugging Face model"""
        try:
            start_time = time.time()
            
            # Load model if not loaded
            if not self.is_loaded:
                if not self.load_model():
                    return self._generate_fallback_response(user_input, context, "Model not available")
            
            # Import required modules
            import torch
            
            # Prepare input
            if self.model_name == "microsoft/DialoGPT-medium":
                # For DialoGPT, use conversation format
                input_text = user_input
                
                # Add conversation history
                if conversation_history:
                    for entry in conversation_history[-2:]:  # Last 2 exchanges
                        input_text = f"{entry.get('user_input', '')} {entry.get('ai_response', '')} {input_text}"
            else:
                # For other models, use a prompt format
                input_text = f"Human: {user_input}\nAssistant:"
            
            # Tokenize input
            inputs = self.tokenizer.encode(input_text, return_tensors="pt")
            
            # Generate response
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs,
                    max_length=inputs.shape[1] + 50,  # Add 50 tokens
                    num_return_sequences=1,
                    temperature=0.8,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id,
                    attention_mask=torch.ones(inputs.shape, dtype=torch.long)
                )
            
            # Decode response
            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # Extract only the new part (remove input)
            if self.model_name == "microsoft/DialoGPT-medium":
                # For DialoGPT, the response includes the input
                ai_response = response[len(input_text):].strip()
            else:
                # For other models, extract after "Assistant:"
                if "Assistant:" in response:
                    ai_response = response.split("Assistant:")[-1].strip()
                else:
                    ai_response = response[len(input_text):].strip()
            
            # Clean up response
            ai_response = self._clean_response(ai_response)
            
            # Post-process response
            processed_response = self._post_process_response(ai_response, context)
            
            generation_time = time.time() - start_time
            
            result = {
                "response": processed_response,
                "response_style": context.get("response_style", "neutral"),
                "generation_time": generation_time,
                "context_used": context,
                "confidence": 0.8
            }
            
            # Update conversation history
            self._update_conversation_history(user_input, processed_response, context)
            
            logger.debug(f"Generated response in {generation_time:.2f}s: '{processed_response[:50]}...'")
            return result
            
        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return self._generate_fallback_response(user_input, context, str(e))
    
    def _clean_response(self, response: str) -> str:
        """Clean up the generated response"""
        # Remove common artifacts
        response = response.replace("<|endoftext|>", "")
        response = response.replace("Human:", "")
        response = response.replace("Assistant:", "")
        
        # Split by sentences and take the first complete one
        sentences = response.split('.')
        if len(sentences) > 1:
            response = sentences[0] + '.'
        
        # Remove extra whitespace
        response = ' '.join(response.split())
        
        return response.strip()
    
    def _post_process_response(self, response: str, context: Dict[str, Any]) -> str:
        """Post-process the generated response"""
        try:
            # Ensure response isn't empty
            if not response or len(response.strip()) < 3:
                return "I understand. Could you tell me more about that?"
            
            # Ensure response isn't too long
            if len(response) > 200:
                sentences = response.split('. ')
                response = sentences[0] + '.'
            
            # Add personality touches based on context
            emotion = context.get("analysis", {}).get("emotion", {}).get("emotion", "neutral")
            
            # Add appropriate emotional responses
            if emotion == "sad" and "sorry" not in response.lower():
                response = response.rstrip('.') + ". I'm here for you."
            elif emotion == "happy" and not any(emoji in response for emoji in ["😊", "🙂", "😄"]):
                response = response.rstrip('.') + ". 😊"
            elif emotion == "excited" and "!" not in response:
                response = response.rstrip('.') + "!"
            
            return response.strip()
            
        except Exception as e:
            logger.error(f"Error post-processing response: {e}")
            return response
    
    def _generate_fallback_response(self, user_input: str, context: Dict[str, Any], error: str = "") -> Dict[str, Any]:
        """Generate fallback response when model fails"""
        fallback_responses = [
            "That's interesting. Tell me more about that.",
            "I understand. How does that make you feel?",
            "That sounds important. Can you share more details?",
            "I'm listening. What's on your mind?",
            "I hear you. Would you like to talk about it more?"
        ]
        
        import random
        response = random.choice(fallback_responses)
        
        return {
            "response": response,
            "response_style": "empathetic",
            "generation_time": 0.1,
            "error": error,
            "confidence": 0.3
        }
    
    def _update_conversation_history(self, user_input: str, ai_response: str, context: Dict[str, Any]):
        """Update conversation history"""
        try:
            entry = {
                "timestamp": time.time(),
                "user_input": user_input,
                "ai_response": ai_response,
                "context": context
            }
            
            self.conversation_history.append(entry)
            
            # Keep only recent history
            max_history = 10
            if len(self.conversation_history) > max_history:
                self.conversation_history = self.conversation_history[-max_history:]
            
        except Exception as e:
            logger.error(f"Error updating conversation history: {e}")
    
    def get_setup_instructions(self) -> str:
        """Get setup instructions for Hugging Face"""
        return """
🤗 HUGGING FACE SETUP INSTRUCTIONS:

1. Install transformers:
   pip install transformers torch

2. The model will download automatically on first use
   - DialoGPT-medium: ~350MB
   - GPT-2: ~500MB
   - T5-small: ~240MB

3. Models are cached locally for offline use

✅ Benefits:
- Completely FREE
- Works offline after download
- No API keys needed
- Privacy-focused
- Multiple model options

⚠️ Note:
- First run downloads the model
- Requires ~1GB disk space
- Slower than cloud APIs but free
"""
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get AI engine statistics"""
        return {
            "total_responses_generated": len(self.conversation_history),
            "personality_traits": self.personality_traits,
            "model_used": self.model_name,
            "model_loaded": self.is_loaded,
            "conversation_length": len(self.conversation_history)
        }
