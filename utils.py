"""
Utility functions for the Human-like Audio AI Desktop Application
"""
import logging
import json
import datetime
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Optional
import threading
import queue

def setup_logging(log_level: str = "INFO", log_file: str = "logs/app.log"):
    """Setup logging configuration"""
    Path(log_file).parent.mkdir(exist_ok=True)
    
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def normalize_audio(audio_data: np.ndarray) -> np.ndarray:
    """Normalize audio data to [-1, 1] range"""
    if audio_data.dtype != np.float32:
        audio_data = audio_data.astype(np.float32)
    
    max_val = np.max(np.abs(audio_data))
    if max_val > 0:
        audio_data = audio_data / max_val
    
    return audio_data

def detect_speech_activity(audio_data: np.ndarray, threshold: float = 0.01) -> bool:
    """Detect if audio contains speech activity"""
    rms = np.sqrt(np.mean(audio_data ** 2))
    return rms > threshold

def calculate_audio_features(audio_data: np.ndarray, sample_rate: int) -> Dict[str, float]:
    """Calculate basic audio features"""
    # RMS Energy
    rms = np.sqrt(np.mean(audio_data ** 2))
    
    # Zero Crossing Rate
    zcr = np.mean(np.abs(np.diff(np.sign(audio_data))))
    
    # Spectral Centroid (simplified)
    fft = np.fft.fft(audio_data)
    magnitude = np.abs(fft)
    freqs = np.fft.fftfreq(len(fft), 1/sample_rate)
    spectral_centroid = np.sum(freqs[:len(freqs)//2] * magnitude[:len(magnitude)//2]) / np.sum(magnitude[:len(magnitude)//2])
    
    return {
        'rms_energy': float(rms),
        'zero_crossing_rate': float(zcr),
        'spectral_centroid': float(spectral_centroid) if not np.isnan(spectral_centroid) else 0.0
    }

def save_json(data: Dict[str, Any], filepath: str):
    """Save data to JSON file"""
    Path(filepath).parent.mkdir(exist_ok=True)
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False, default=str)

def load_json(filepath: str) -> Optional[Dict[str, Any]]:
    """Load data from JSON file"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        return None

def format_timestamp(timestamp: Optional[datetime.datetime] = None) -> str:
    """Format timestamp for display"""
    if timestamp is None:
        timestamp = datetime.datetime.now()
    return timestamp.strftime("%Y-%m-%d %H:%M:%S")

def truncate_text(text: str, max_length: int = 100) -> str:
    """Truncate text to specified length"""
    if len(text) <= max_length:
        return text
    return text[:max_length-3] + "..."

class ThreadSafeQueue:
    """Thread-safe queue wrapper"""
    def __init__(self, maxsize: int = 0):
        self._queue = queue.Queue(maxsize=maxsize)
        self._lock = threading.Lock()
    
    def put(self, item: Any, block: bool = True, timeout: Optional[float] = None):
        """Put item in queue"""
        with self._lock:
            self._queue.put(item, block=block, timeout=timeout)
    
    def get(self, block: bool = True, timeout: Optional[float] = None) -> Any:
        """Get item from queue"""
        with self._lock:
            return self._queue.get(block=block, timeout=timeout)
    
    def empty(self) -> bool:
        """Check if queue is empty"""
        with self._lock:
            return self._queue.empty()
    
    def qsize(self) -> int:
        """Get queue size"""
        with self._lock:
            return self._queue.qsize()

class CircularBuffer:
    """Circular buffer for audio data"""
    def __init__(self, size: int):
        self.size = size
        self.buffer = np.zeros(size, dtype=np.float32)
        self.index = 0
        self.full = False
        self._lock = threading.Lock()
    
    def append(self, data: np.ndarray):
        """Append data to buffer"""
        with self._lock:
            data_len = len(data)
            
            if data_len >= self.size:
                # If data is larger than buffer, take the last part
                self.buffer = data[-self.size:].copy()
                self.index = 0
                self.full = True
            else:
                # Normal append
                end_index = self.index + data_len
                
                if end_index <= self.size:
                    self.buffer[self.index:end_index] = data
                    self.index = end_index
                else:
                    # Wrap around
                    first_part = self.size - self.index
                    self.buffer[self.index:] = data[:first_part]
                    self.buffer[:data_len - first_part] = data[first_part:]
                    self.index = data_len - first_part
                    self.full = True
                
                if self.index >= self.size:
                    self.index = 0
                    self.full = True
    
    def get_data(self) -> np.ndarray:
        """Get current buffer data in correct order"""
        with self._lock:
            if not self.full:
                return self.buffer[:self.index].copy()
            else:
                return np.concatenate([
                    self.buffer[self.index:],
                    self.buffer[:self.index]
                ])
    
    def clear(self):
        """Clear the buffer"""
        with self._lock:
            self.buffer.fill(0)
            self.index = 0
            self.full = False
