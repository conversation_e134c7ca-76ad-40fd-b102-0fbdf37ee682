"""
Learning system for continuous improvement and personalization
"""
import logging
import time
import json
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.cluster import KMeans
import pickle
from pathlib import Path
from config import Config
from database import DatabaseManager

logger = logging.getLogger(__name__)

class LearningSystem:
    """Machine learning system for improving responses and personalization"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.models_dir = Config.MODELS_DIR
        self.models_dir.mkdir(exist_ok=True)
        
        # Learning components
        self.response_quality_model = None
        self.user_preference_model = None
        self.context_similarity_model = None
        
        # Vectorizers
        self.text_vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')
        self.response_vectorizer = TfidfVectorizer(max_features=500, stop_words='english')
        
        # Learning data
        self.interaction_patterns = []
        self.successful_responses = []
        self.user_feedback_data = []
        
        # Personalization data
        self.user_preferences = {
            "preferred_response_styles": {},
            "topic_interests": {},
            "emotional_patterns": {},
            "interaction_times": [],
            "response_length_preference": "medium"
        }
        
        # Learning statistics
        self.total_learning_sessions = 0
        self.improvement_score = 0.0
        self.last_learning_update = None
        
        logger.info("Learning system initialized")
    
    def load_models(self):
        """Load existing models if available"""
        try:
            # Load response quality model
            quality_model_path = self.models_dir / "response_quality_model.pkl"
            if quality_model_path.exists():
                with open(quality_model_path, 'rb') as f:
                    self.response_quality_model = pickle.load(f)
                logger.info("Loaded response quality model")
            
            # Load user preference model
            preference_model_path = self.models_dir / "user_preference_model.pkl"
            if preference_model_path.exists():
                with open(preference_model_path, 'rb') as f:
                    self.user_preference_model = pickle.load(f)
                logger.info("Loaded user preference model")
            
            # Load vectorizers
            vectorizer_path = self.models_dir / "text_vectorizer.pkl"
            if vectorizer_path.exists():
                with open(vectorizer_path, 'rb') as f:
                    self.text_vectorizer = pickle.load(f)
                logger.info("Loaded text vectorizer")
            
            # Load user preferences
            preferences_path = self.models_dir / "user_preferences.json"
            if preferences_path.exists():
                with open(preferences_path, 'r') as f:
                    self.user_preferences.update(json.load(f))
                logger.info("Loaded user preferences")
            
            return True
            
        except Exception as e:
            logger.error(f"Error loading models: {e}")
            return False
    
    def save_models(self):
        """Save trained models"""
        try:
            # Save response quality model
            if self.response_quality_model:
                quality_model_path = self.models_dir / "response_quality_model.pkl"
                with open(quality_model_path, 'wb') as f:
                    pickle.dump(self.response_quality_model, f)
            
            # Save user preference model
            if self.user_preference_model:
                preference_model_path = self.models_dir / "user_preference_model.pkl"
                with open(preference_model_path, 'wb') as f:
                    pickle.dump(self.user_preference_model, f)
            
            # Save vectorizers
            vectorizer_path = self.models_dir / "text_vectorizer.pkl"
            with open(vectorizer_path, 'wb') as f:
                pickle.dump(self.text_vectorizer, f)
            
            # Save user preferences
            preferences_path = self.models_dir / "user_preferences.json"
            with open(preferences_path, 'w') as f:
                json.dump(self.user_preferences, f, indent=2, default=str)
            
            logger.info("Models saved successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error saving models: {e}")
            return False
    
    def record_interaction(self, 
                          user_input: str,
                          ai_response: str,
                          context: Dict[str, Any],
                          user_feedback: Optional[float] = None):
        """Record an interaction for learning"""
        try:
            interaction = {
                "timestamp": time.time(),
                "user_input": user_input,
                "ai_response": ai_response,
                "context": context,
                "user_feedback": user_feedback,
                "response_length": len(ai_response),
                "response_style": context.get("response_style", "neutral")
            }
            
            self.interaction_patterns.append(interaction)
            
            # Store in database
            self.db_manager.save_conversation(
                audio_input="",  # Will be filled by main app
                transcribed_text=user_input,
                detected_emotion=context.get("analysis", {}).get("emotion", {}).get("emotion", "neutral"),
                ai_response=ai_response,
                response_style=context.get("response_style", "neutral"),
                user_feedback=user_feedback,
                context_data=context
            )
            
            # Update user preferences
            self._update_user_preferences(interaction)
            
            # If feedback is positive, mark as successful response
            if user_feedback and user_feedback > Config.FEEDBACK_THRESHOLD:
                self.successful_responses.append(interaction)
            
            logger.debug(f"Recorded interaction with feedback: {user_feedback}")
            
        except Exception as e:
            logger.error(f"Error recording interaction: {e}")
    
    def _update_user_preferences(self, interaction: Dict[str, Any]):
        """Update user preferences based on interaction"""
        try:
            response_style = interaction.get("response_style", "neutral")
            feedback = interaction.get("user_feedback")
            context = interaction.get("context", {})
            
            # Update response style preferences
            if feedback is not None:
                if response_style not in self.user_preferences["preferred_response_styles"]:
                    self.user_preferences["preferred_response_styles"][response_style] = []
                
                self.user_preferences["preferred_response_styles"][response_style].append(feedback)
            
            # Update topic interests
            topics = context.get("analysis", {}).get("topics", [])
            for topic in topics:
                if topic not in self.user_preferences["topic_interests"]:
                    self.user_preferences["topic_interests"][topic] = 0
                self.user_preferences["topic_interests"][topic] += 1
            
            # Update emotional patterns
            emotion = context.get("analysis", {}).get("emotion", {}).get("emotion")
            if emotion:
                if emotion not in self.user_preferences["emotional_patterns"]:
                    self.user_preferences["emotional_patterns"][emotion] = []
                
                self.user_preferences["emotional_patterns"][emotion].append({
                    "timestamp": interaction["timestamp"],
                    "response_quality": feedback or 0.5
                })
            
            # Update interaction times
            self.user_preferences["interaction_times"].append(interaction["timestamp"])
            
            # Keep only recent data
            max_history = 1000
            if len(self.user_preferences["interaction_times"]) > max_history:
                self.user_preferences["interaction_times"] = self.user_preferences["interaction_times"][-max_history:]
            
        except Exception as e:
            logger.error(f"Error updating user preferences: {e}")
    
    def get_response_recommendation(self, user_input: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Get response recommendations based on learning"""
        try:
            recommendations = {
                "preferred_style": "neutral",
                "suggested_length": "medium",
                "confidence": 0.5,
                "similar_contexts": []
            }
            
            # Analyze user preferences
            emotion = context.get("analysis", {}).get("emotion", {}).get("emotion", "neutral")
            topics = context.get("analysis", {}).get("topics", [])
            
            # Recommend response style based on past feedback
            style_scores = {}
            for style, feedback_list in self.user_preferences["preferred_response_styles"].items():
                if feedback_list:
                    avg_feedback = np.mean(feedback_list)
                    style_scores[style] = avg_feedback
            
            if style_scores:
                best_style = max(style_scores, key=style_scores.get)
                recommendations["preferred_style"] = best_style
                recommendations["confidence"] = min(0.9, style_scores[best_style])
            
            # Find similar past contexts
            similar_contexts = self._find_similar_contexts(user_input, context)
            recommendations["similar_contexts"] = similar_contexts[:3]  # Top 3
            
            # Recommend response length based on user engagement
            recommendations["suggested_length"] = self._recommend_response_length(context)
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error getting response recommendation: {e}")
            return {"preferred_style": "neutral", "suggested_length": "medium", "confidence": 0.5}
    
    def _find_similar_contexts(self, user_input: str, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Find similar past contexts"""
        try:
            if not self.interaction_patterns:
                return []
            
            # Vectorize current input
            current_vector = self.text_vectorizer.fit_transform([user_input])
            
            # Vectorize past inputs
            past_inputs = [interaction["user_input"] for interaction in self.interaction_patterns]
            if not past_inputs:
                return []
            
            past_vectors = self.text_vectorizer.transform(past_inputs)
            
            # Calculate similarities
            similarities = cosine_similarity(current_vector, past_vectors)[0]
            
            # Get top similar interactions
            similar_indices = np.argsort(similarities)[::-1][:5]
            
            similar_contexts = []
            for idx in similar_indices:
                if similarities[idx] > 0.3:  # Minimum similarity threshold
                    interaction = self.interaction_patterns[idx]
                    similar_contexts.append({
                        "similarity": float(similarities[idx]),
                        "user_input": interaction["user_input"],
                        "ai_response": interaction["ai_response"],
                        "feedback": interaction.get("user_feedback"),
                        "response_style": interaction.get("response_style")
                    })
            
            return similar_contexts
            
        except Exception as e:
            logger.error(f"Error finding similar contexts: {e}")
            return []
    
    def _recommend_response_length(self, context: Dict[str, Any]) -> str:
        """Recommend response length based on context and user preferences"""
        try:
            # Analyze past successful response lengths
            if self.successful_responses:
                lengths = [len(resp["ai_response"]) for resp in self.successful_responses]
                avg_length = np.mean(lengths)
                
                if avg_length < 50:
                    return "short"
                elif avg_length > 150:
                    return "long"
                else:
                    return "medium"
            
            # Default based on emotion and intent
            emotion = context.get("analysis", {}).get("emotion", {}).get("emotion", "neutral")
            intent = context.get("analysis", {}).get("intent", {}).get("intent", "statement")
            
            if emotion in ["sad", "anxious"] or intent == "complaint":
                return "long"  # More supportive response
            elif emotion in ["happy", "excited"]:
                return "medium"
            else:
                return "medium"
                
        except Exception as e:
            logger.error(f"Error recommending response length: {e}")
            return "medium"
    
    def train_models(self):
        """Train learning models with accumulated data"""
        try:
            if len(self.interaction_patterns) < 10:
                logger.info("Not enough data for training (minimum 10 interactions)")
                return False
            
            logger.info("Training learning models...")
            
            # Prepare training data
            inputs = [interaction["user_input"] for interaction in self.interaction_patterns]
            responses = [interaction["ai_response"] for interaction in self.interaction_patterns]
            feedbacks = [interaction.get("user_feedback", 0.5) for interaction in self.interaction_patterns]
            
            # Train text vectorizer
            self.text_vectorizer.fit(inputs)
            
            # Train response quality model (simple clustering for now)
            if len(self.successful_responses) >= 3:
                successful_inputs = [resp["user_input"] for resp in self.successful_responses]
                successful_vectors = self.text_vectorizer.transform(successful_inputs)
                
                # Cluster successful responses
                n_clusters = min(3, len(self.successful_responses))
                self.response_quality_model = KMeans(n_clusters=n_clusters, random_state=42)
                self.response_quality_model.fit(successful_vectors)
            
            # Update learning statistics
            self.total_learning_sessions += 1
            self.last_learning_update = time.time()
            
            # Calculate improvement score
            if feedbacks:
                self.improvement_score = np.mean(feedbacks)
            
            # Save models
            self.save_models()
            
            logger.info(f"Training completed. Improvement score: {self.improvement_score:.3f}")
            return True
            
        except Exception as e:
            logger.error(f"Error training models: {e}")
            return False
    
    def get_learning_insights(self) -> Dict[str, Any]:
        """Get insights from learning data"""
        try:
            insights = {
                "total_interactions": len(self.interaction_patterns),
                "successful_responses": len(self.successful_responses),
                "improvement_score": self.improvement_score,
                "preferred_styles": {},
                "common_topics": {},
                "emotional_trends": {},
                "learning_progress": "stable"
            }
            
            # Analyze preferred response styles
            for style, feedback_list in self.user_preferences["preferred_response_styles"].items():
                if feedback_list:
                    insights["preferred_styles"][style] = {
                        "average_feedback": np.mean(feedback_list),
                        "usage_count": len(feedback_list)
                    }
            
            # Analyze common topics
            insights["common_topics"] = dict(
                sorted(self.user_preferences["topic_interests"].items(), 
                       key=lambda x: x[1], reverse=True)[:10]
            )
            
            # Analyze emotional trends
            for emotion, pattern_list in self.user_preferences["emotional_patterns"].items():
                if pattern_list:
                    recent_quality = [p["response_quality"] for p in pattern_list[-5:]]
                    insights["emotional_trends"][emotion] = {
                        "frequency": len(pattern_list),
                        "recent_response_quality": np.mean(recent_quality)
                    }
            
            # Determine learning progress
            if self.improvement_score > 0.7:
                insights["learning_progress"] = "excellent"
            elif self.improvement_score > 0.6:
                insights["learning_progress"] = "good"
            elif self.improvement_score > 0.5:
                insights["learning_progress"] = "improving"
            else:
                insights["learning_progress"] = "needs_attention"
            
            return insights
            
        except Exception as e:
            logger.error(f"Error getting learning insights: {e}")
            return {"error": str(e)}
    
    def reset_learning_data(self):
        """Reset learning data (for testing or fresh start)"""
        try:
            self.interaction_patterns.clear()
            self.successful_responses.clear()
            self.user_feedback_data.clear()
            
            self.user_preferences = {
                "preferred_response_styles": {},
                "topic_interests": {},
                "emotional_patterns": {},
                "interaction_times": [],
                "response_length_preference": "medium"
            }
            
            self.total_learning_sessions = 0
            self.improvement_score = 0.0
            self.last_learning_update = None
            
            logger.info("Learning data reset")
            
        except Exception as e:
            logger.error(f"Error resetting learning data: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get learning system statistics"""
        return {
            "total_interactions": len(self.interaction_patterns),
            "successful_responses": len(self.successful_responses),
            "total_learning_sessions": self.total_learning_sessions,
            "improvement_score": self.improvement_score,
            "last_learning_update": self.last_learning_update,
            "models_trained": {
                "response_quality": self.response_quality_model is not None,
                "user_preference": self.user_preference_model is not None
            },
            "user_preferences_count": {
                "response_styles": len(self.user_preferences["preferred_response_styles"]),
                "topics": len(self.user_preferences["topic_interests"]),
                "emotions": len(self.user_preferences["emotional_patterns"])
            }
        }
