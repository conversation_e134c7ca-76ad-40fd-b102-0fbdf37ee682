"""
Database layer for storing conversations and learning data
"""
import sqlite3
import json
import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import logging
from config import Config

logger = logging.getLogger(__name__)

class DatabaseManager:
    """Manages SQLite database for conversation storage and learning"""
    
    def __init__(self, db_path: str = None):
        self.db_path = db_path or Config.DATABASE_PATH
        self.db_path.parent.mkdir(exist_ok=True)
        self.init_database()
    
    def init_database(self):
        """Initialize database tables"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Conversations table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS conversations (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        audio_input TEXT,
                        transcribed_text TEXT,
                        detected_emotion TEXT,
                        ai_response TEXT,
                        response_style TEXT,
                        user_feedback REAL,
                        context_data TEXT,
                        audio_features TEXT
                    )
                ''')
                
                # Learning data table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS learning_data (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        input_pattern TEXT,
                        successful_response TEXT,
                        feedback_score REAL,
                        context_tags TEXT,
                        improvement_notes TEXT
                    )
                ''')
                
                # User preferences table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS user_preferences (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        preference_key TEXT UNIQUE,
                        preference_value TEXT,
                        last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Audio sessions table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS audio_sessions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        session_start DATETIME DEFAULT CURRENT_TIMESTAMP,
                        session_end DATETIME,
                        total_interactions INTEGER DEFAULT 0,
                        avg_response_time REAL,
                        session_notes TEXT
                    )
                ''')
                
                conn.commit()
                logger.info("Database initialized successfully")
                
        except sqlite3.Error as e:
            logger.error(f"Database initialization error: {e}")
            raise
    
    def save_conversation(self, 
                         audio_input: str,
                         transcribed_text: str,
                         detected_emotion: str,
                         ai_response: str,
                         response_style: str,
                         user_feedback: float = None,
                         context_data: Dict[str, Any] = None,
                         audio_features: Dict[str, float] = None) -> int:
        """Save a conversation to the database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO conversations 
                    (audio_input, transcribed_text, detected_emotion, ai_response, 
                     response_style, user_feedback, context_data, audio_features)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    audio_input,
                    transcribed_text,
                    detected_emotion,
                    ai_response,
                    response_style,
                    user_feedback,
                    json.dumps(context_data) if context_data else None,
                    json.dumps(audio_features) if audio_features else None
                ))
                
                conversation_id = cursor.lastrowid
                conn.commit()
                logger.debug(f"Conversation saved with ID: {conversation_id}")
                return conversation_id
                
        except sqlite3.Error as e:
            logger.error(f"Error saving conversation: {e}")
            return -1
    
    def get_recent_conversations(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent conversations for context"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM conversations 
                    ORDER BY timestamp DESC 
                    LIMIT ?
                ''', (limit,))
                
                columns = [description[0] for description in cursor.description]
                conversations = []
                
                for row in cursor.fetchall():
                    conv = dict(zip(columns, row))
                    # Parse JSON fields
                    if conv['context_data']:
                        conv['context_data'] = json.loads(conv['context_data'])
                    if conv['audio_features']:
                        conv['audio_features'] = json.loads(conv['audio_features'])
                    conversations.append(conv)
                
                return conversations
                
        except sqlite3.Error as e:
            logger.error(f"Error retrieving conversations: {e}")
            return []
    
    def save_learning_data(self,
                          input_pattern: str,
                          successful_response: str,
                          feedback_score: float,
                          context_tags: List[str] = None,
                          improvement_notes: str = None) -> int:
        """Save learning data for model improvement"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO learning_data 
                    (input_pattern, successful_response, feedback_score, context_tags, improvement_notes)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    input_pattern,
                    successful_response,
                    feedback_score,
                    json.dumps(context_tags) if context_tags else None,
                    improvement_notes
                ))
                
                learning_id = cursor.lastrowid
                conn.commit()
                logger.debug(f"Learning data saved with ID: {learning_id}")
                return learning_id
                
        except sqlite3.Error as e:
            logger.error(f"Error saving learning data: {e}")
            return -1
    
    def get_user_preference(self, key: str) -> Optional[str]:
        """Get user preference by key"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT preference_value FROM user_preferences 
                    WHERE preference_key = ?
                ''', (key,))
                
                result = cursor.fetchone()
                return result[0] if result else None
                
        except sqlite3.Error as e:
            logger.error(f"Error retrieving user preference: {e}")
            return None
    
    def set_user_preference(self, key: str, value: str):
        """Set user preference"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO user_preferences 
                    (preference_key, preference_value, last_updated)
                    VALUES (?, ?, CURRENT_TIMESTAMP)
                ''', (key, value))
                
                conn.commit()
                logger.debug(f"User preference set: {key} = {value}")
                
        except sqlite3.Error as e:
            logger.error(f"Error setting user preference: {e}")
    
    def get_conversation_stats(self) -> Dict[str, Any]:
        """Get conversation statistics"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Total conversations
                cursor.execute('SELECT COUNT(*) FROM conversations')
                total_conversations = cursor.fetchone()[0]
                
                # Average feedback score
                cursor.execute('SELECT AVG(user_feedback) FROM conversations WHERE user_feedback IS NOT NULL')
                avg_feedback = cursor.fetchone()[0] or 0
                
                # Most common emotions
                cursor.execute('''
                    SELECT detected_emotion, COUNT(*) as count 
                    FROM conversations 
                    WHERE detected_emotion IS NOT NULL
                    GROUP BY detected_emotion 
                    ORDER BY count DESC 
                    LIMIT 5
                ''')
                common_emotions = cursor.fetchall()
                
                # Recent activity (last 7 days)
                cursor.execute('''
                    SELECT COUNT(*) FROM conversations 
                    WHERE timestamp >= datetime('now', '-7 days')
                ''')
                recent_activity = cursor.fetchone()[0]
                
                return {
                    'total_conversations': total_conversations,
                    'average_feedback': round(avg_feedback, 2),
                    'common_emotions': common_emotions,
                    'recent_activity': recent_activity
                }
                
        except sqlite3.Error as e:
            logger.error(f"Error getting conversation stats: {e}")
            return {}
    
    def cleanup_old_data(self, days_to_keep: int = 30):
        """Clean up old conversation data"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    DELETE FROM conversations 
                    WHERE timestamp < datetime('now', '-{} days')
                '''.format(days_to_keep))
                
                deleted_count = cursor.rowcount
                conn.commit()
                logger.info(f"Cleaned up {deleted_count} old conversation records")
                
        except sqlite3.Error as e:
            logger.error(f"Error cleaning up old data: {e}")
    
    def close(self):
        """Close database connection"""
        # SQLite connections are automatically closed when using context manager
        pass
