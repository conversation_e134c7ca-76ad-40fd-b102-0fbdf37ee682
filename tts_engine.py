"""
Text-to-Speech engine for natural voice synthesis
"""
import pyttsx3
import logging
import threading
import time
import queue
from typing import Dict, Any, Optional, Callable
from config import Config

logger = logging.getLogger(__name__)

class TextToSpeechEngine:
    """Text-to-Speech engine with emotional tone matching"""
    
    def __init__(self):
        self.engine = None
        self.is_initialized = False
        self.is_speaking = False
        
        # Speech queue for managing multiple requests
        self.speech_queue = queue.Queue()
        self.speech_thread = None
        self.stop_speaking = threading.Event()
        
        # Voice settings
        self.rate = Config.TTS_RATE
        self.volume = Config.TTS_VOLUME
        self.voice_id = None
        
        # Emotional voice adjustments
        self.emotion_adjustments = {
            "happy": {"rate_modifier": 1.1, "volume_modifier": 1.0},
            "sad": {"rate_modifier": 0.8, "volume_modifier": 0.9},
            "excited": {"rate_modifier": 1.3, "volume_modifier": 1.0},
            "angry": {"rate_modifier": 1.2, "volume_modifier": 1.0},
            "calm": {"rate_modifier": 0.9, "volume_modifier": 0.95},
            "neutral": {"rate_modifier": 1.0, "volume_modifier": 1.0}
        }
        
        # Statistics
        self.total_speeches = 0
        self.total_speech_time = 0
        
        logger.info("TTS Engine initialized")
    
    def initialize_engine(self) -> bool:
        """Initialize the TTS engine"""
        try:
            if self.engine is None:
                self.engine = pyttsx3.init()
                
                # Set properties
                self.engine.setProperty('rate', self.rate)
                self.engine.setProperty('volume', self.volume)
                
                # Get available voices
                voices = self.engine.getProperty('voices')
                if voices:
                    # Prefer female voice if available
                    for voice in voices:
                        if 'female' in voice.name.lower() or 'zira' in voice.name.lower():
                            self.voice_id = voice.id
                            break
                    
                    if self.voice_id:
                        self.engine.setProperty('voice', self.voice_id)
                        logger.info(f"Selected voice: {self.voice_id}")
                    else:
                        # Use first available voice
                        self.engine.setProperty('voice', voices[0].id)
                        logger.info(f"Using default voice: {voices[0].id}")
                
                self.is_initialized = True
                logger.info("TTS engine initialized successfully")
                
                # Start speech worker thread
                self._start_speech_worker()
                
                return True
                
        except Exception as e:
            logger.error(f"Error initializing TTS engine: {e}")
            self.is_initialized = False
            return False
    
    def _start_speech_worker(self):
        """Start the speech worker thread"""
        if self.speech_thread is None or not self.speech_thread.is_alive():
            self.stop_speaking.clear()
            self.speech_thread = threading.Thread(target=self._speech_worker, daemon=True)
            self.speech_thread.start()
            logger.debug("Speech worker thread started")
    
    def _speech_worker(self):
        """Worker thread for processing speech queue"""
        while not self.stop_speaking.is_set():
            try:
                # Get speech request from queue (with timeout)
                speech_request = self.speech_queue.get(timeout=1.0)
                
                if speech_request is None:  # Shutdown signal
                    break
                
                text = speech_request.get("text", "")
                emotion = speech_request.get("emotion", "neutral")
                callback = speech_request.get("callback")
                
                if text:
                    self._speak_text_internal(text, emotion, callback)
                
                self.speech_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Error in speech worker: {e}")
    
    def _speak_text_internal(self, text: str, emotion: str = "neutral", callback: Optional[Callable] = None):
        """Internal method to speak text with emotion"""
        try:
            if not self.is_initialized:
                if not self.initialize_engine():
                    return
            
            self.is_speaking = True
            start_time = time.time()
            
            # Adjust voice properties based on emotion
            self._adjust_voice_for_emotion(emotion)
            
            # Speak the text
            self.engine.say(text)
            self.engine.runAndWait()
            
            speech_time = time.time() - start_time
            self.total_speeches += 1
            self.total_speech_time += speech_time
            
            logger.debug(f"Spoke text with {emotion} emotion in {speech_time:.2f}s: '{text[:50]}...'")
            
            # Call callback if provided
            if callback:
                try:
                    callback(text, emotion, speech_time)
                except Exception as e:
                    logger.error(f"Error in TTS callback: {e}")
            
        except Exception as e:
            logger.error(f"Error speaking text: {e}")
        finally:
            self.is_speaking = False
            # Reset voice properties
            self._reset_voice_properties()
    
    def _adjust_voice_for_emotion(self, emotion: str):
        """Adjust voice properties based on emotion"""
        try:
            if emotion in self.emotion_adjustments:
                adjustments = self.emotion_adjustments[emotion]
                
                # Adjust rate
                new_rate = int(self.rate * adjustments["rate_modifier"])
                self.engine.setProperty('rate', new_rate)
                
                # Adjust volume
                new_volume = min(1.0, self.volume * adjustments["volume_modifier"])
                self.engine.setProperty('volume', new_volume)
                
                logger.debug(f"Adjusted voice for {emotion}: rate={new_rate}, volume={new_volume:.2f}")
            
        except Exception as e:
            logger.error(f"Error adjusting voice for emotion: {e}")
    
    def _reset_voice_properties(self):
        """Reset voice properties to default"""
        try:
            self.engine.setProperty('rate', self.rate)
            self.engine.setProperty('volume', self.volume)
        except Exception as e:
            logger.error(f"Error resetting voice properties: {e}")
    
    def speak(self, text: str, emotion: str = "neutral", callback: Optional[Callable] = None, priority: bool = False):
        """Queue text for speech synthesis"""
        try:
            if not Config.TTS_ENABLED:
                logger.debug("TTS is disabled")
                return
            
            if not text.strip():
                logger.warning("Empty text provided for TTS")
                return
            
            speech_request = {
                "text": text,
                "emotion": emotion,
                "callback": callback
            }
            
            if priority:
                # Clear queue and add high priority item
                while not self.speech_queue.empty():
                    try:
                        self.speech_queue.get_nowait()
                    except queue.Empty:
                        break
            
            self.speech_queue.put(speech_request)
            logger.debug(f"Queued text for speech: '{text[:50]}...' with emotion: {emotion}")
            
        except Exception as e:
            logger.error(f"Error queuing text for speech: {e}")
    
    def speak_immediately(self, text: str, emotion: str = "neutral", callback: Optional[Callable] = None):
        """Speak text immediately (blocking)"""
        try:
            if not Config.TTS_ENABLED:
                return
            
            self.stop_current_speech()
            self._speak_text_internal(text, emotion, callback)
            
        except Exception as e:
            logger.error(f"Error speaking immediately: {e}")
    
    def stop_current_speech(self):
        """Stop current speech"""
        try:
            if self.engine and self.is_speaking:
                self.engine.stop()
                self.is_speaking = False
                logger.debug("Stopped current speech")
        except Exception as e:
            logger.error(f"Error stopping speech: {e}")
    
    def clear_speech_queue(self):
        """Clear the speech queue"""
        try:
            while not self.speech_queue.empty():
                try:
                    self.speech_queue.get_nowait()
                except queue.Empty:
                    break
            logger.debug("Cleared speech queue")
        except Exception as e:
            logger.error(f"Error clearing speech queue: {e}")
    
    def set_rate(self, rate: int):
        """Set speech rate"""
        try:
            self.rate = max(50, min(400, rate))  # Clamp between 50-400
            if self.engine:
                self.engine.setProperty('rate', self.rate)
            logger.info(f"Set speech rate to {self.rate}")
        except Exception as e:
            logger.error(f"Error setting speech rate: {e}")
    
    def set_volume(self, volume: float):
        """Set speech volume"""
        try:
            self.volume = max(0.0, min(1.0, volume))  # Clamp between 0-1
            if self.engine:
                self.engine.setProperty('volume', self.volume)
            logger.info(f"Set speech volume to {self.volume}")
        except Exception as e:
            logger.error(f"Error setting speech volume: {e}")
    
    def get_available_voices(self) -> list:
        """Get list of available voices"""
        try:
            if not self.is_initialized:
                if not self.initialize_engine():
                    return []
            
            voices = self.engine.getProperty('voices')
            voice_list = []
            
            for voice in voices:
                voice_info = {
                    "id": voice.id,
                    "name": voice.name,
                    "languages": getattr(voice, 'languages', []),
                    "gender": getattr(voice, 'gender', 'unknown')
                }
                voice_list.append(voice_info)
            
            return voice_list
            
        except Exception as e:
            logger.error(f"Error getting available voices: {e}")
            return []
    
    def set_voice(self, voice_id: str):
        """Set voice by ID"""
        try:
            if self.engine:
                self.engine.setProperty('voice', voice_id)
                self.voice_id = voice_id
                logger.info(f"Set voice to {voice_id}")
        except Exception as e:
            logger.error(f"Error setting voice: {e}")
    
    def test_speech(self, test_emotions: bool = True):
        """Test TTS with different emotions"""
        logger.info("Testing TTS engine...")
        
        if not self.initialize_engine():
            logger.error("Failed to initialize TTS engine for testing")
            return
        
        test_text = "Hello! I am your AI assistant and I'm ready to help you."
        
        if test_emotions:
            emotions_to_test = ["neutral", "happy", "sad", "excited", "calm"]
            for emotion in emotions_to_test:
                logger.info(f"Testing {emotion} emotion...")
                self.speak_immediately(f"This is a test of the {emotion} emotion.", emotion)
                time.sleep(1)
        else:
            self.speak_immediately(test_text)
        
        logger.info("TTS test completed")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get TTS statistics"""
        avg_speech_time = (
            self.total_speech_time / self.total_speeches 
            if self.total_speeches > 0 else 0
        )
        
        return {
            "is_initialized": self.is_initialized,
            "is_speaking": self.is_speaking,
            "total_speeches": self.total_speeches,
            "total_speech_time": self.total_speech_time,
            "average_speech_time": avg_speech_time,
            "queue_size": self.speech_queue.qsize(),
            "current_rate": self.rate,
            "current_volume": self.volume,
            "voice_id": self.voice_id
        }
    
    def shutdown(self):
        """Shutdown TTS engine"""
        try:
            # Stop speech worker
            self.stop_speaking.set()
            self.speech_queue.put(None)  # Shutdown signal
            
            if self.speech_thread and self.speech_thread.is_alive():
                self.speech_thread.join(timeout=2.0)
            
            # Stop current speech
            self.stop_current_speech()
            
            # Cleanup engine
            if self.engine:
                try:
                    self.engine.stop()
                except:
                    pass
                self.engine = None
            
            self.is_initialized = False
            logger.info("TTS engine shutdown completed")
            
        except Exception as e:
            logger.error(f"Error shutting down TTS engine: {e}")
