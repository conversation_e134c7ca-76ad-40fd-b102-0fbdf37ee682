"""
Audio capture module for capturing system/desktop audio
"""
import sounddevice as sd
import numpy as np
import threading
import time
import logging
from typing import Callable, Optional
from config import Config
from utils import CircularBuffer, detect_speech_activity, calculate_audio_features

logger = logging.getLogger(__name__)

class SystemAudioCapture:
    """Captures system audio in real-time"""
    
    def __init__(self, 
                 sample_rate: int = Config.SAMPLE_RATE,
                 channels: int = Config.CHANNELS,
                 chunk_size: int = Config.CHUNK_SIZE,
                 buffer_duration: float = Config.BUFFER_DURATION):
        
        self.sample_rate = sample_rate
        self.channels = channels
        self.chunk_size = chunk_size
        self.buffer_duration = buffer_duration
        
        # Calculate buffer size
        self.buffer_size = int(sample_rate * buffer_duration)
        self.audio_buffer = CircularBuffer(self.buffer_size)
        
        # Threading
        self.is_capturing = False
        self.capture_thread = None
        self._stop_event = threading.Event()
        
        # Callbacks
        self.audio_callback: Optional[Callable] = None
        self.speech_detected_callback: Optional[Callable] = None
        
        # Audio processing
        self.speech_threshold = Config.SPEECH_THRESHOLD
        self.last_speech_time = 0
        self.silence_duration = Config.SILENCE_DURATION
        
        # Statistics
        self.total_samples = 0
        self.speech_detected_count = 0
        
        logger.info(f"Audio capture initialized: {sample_rate}Hz, {channels} channels")
    
    def list_audio_devices(self):
        """List available audio devices"""
        try:
            devices = sd.query_devices()
            logger.info("Available audio devices:")
            for i, device in enumerate(devices):
                logger.info(f"  {i}: {device['name']} - {device['max_input_channels']} in, {device['max_output_channels']} out")
            return devices
        except Exception as e:
            logger.error(f"Error listing audio devices: {e}")
            return []
    
    def find_system_audio_device(self):
        """Find system audio device (loopback/stereo mix)"""
        try:
            devices = sd.query_devices()
            
            # Look for common system audio device names
            system_keywords = ['stereo mix', 'loopback', 'what u hear', 'wave out mix', 'speakers']
            
            for i, device in enumerate(devices):
                device_name = device['name'].lower()
                if any(keyword in device_name for keyword in system_keywords):
                    if device['max_input_channels'] > 0:
                        logger.info(f"Found system audio device: {device['name']}")
                        return i
            
            # If no specific system audio device found, use default
            logger.warning("No system audio device found, using default input device")
            return sd.default.device[0]
            
        except Exception as e:
            logger.error(f"Error finding system audio device: {e}")
            return None
    
    def audio_callback_internal(self, indata, frames, time, status):
        """Internal audio callback for processing captured audio"""
        if status:
            logger.warning(f"Audio callback status: {status}")
        
        try:
            # Convert to mono if stereo
            if indata.shape[1] > 1:
                audio_data = np.mean(indata, axis=1)
            else:
                audio_data = indata[:, 0]
            
            # Add to buffer
            self.audio_buffer.append(audio_data)
            self.total_samples += len(audio_data)
            
            # Detect speech activity
            has_speech = detect_speech_activity(audio_data, self.speech_threshold)
            
            if has_speech:
                self.last_speech_time = time.currentTime
                self.speech_detected_count += 1
                
                # Call speech detected callback
                if self.speech_detected_callback:
                    try:
                        self.speech_detected_callback(audio_data, time.currentTime)
                    except Exception as e:
                        logger.error(f"Error in speech detected callback: {e}")
            
            # Check for silence period (potential end of speech)
            current_time = time.currentTime
            if (self.last_speech_time > 0 and 
                current_time - self.last_speech_time > self.silence_duration):
                
                # Get accumulated audio data
                accumulated_audio = self.audio_buffer.get_data()
                
                if len(accumulated_audio) > 0:
                    # Calculate audio features
                    features = calculate_audio_features(accumulated_audio, self.sample_rate)
                    
                    # Call main audio callback with accumulated data
                    if self.audio_callback:
                        try:
                            self.audio_callback(accumulated_audio, features, current_time)
                        except Exception as e:
                            logger.error(f"Error in audio callback: {e}")
                    
                    # Clear buffer after processing
                    self.audio_buffer.clear()
                
                # Reset speech detection
                self.last_speech_time = 0
            
        except Exception as e:
            logger.error(f"Error in audio callback: {e}")
    
    def start_capture(self, device_id: Optional[int] = None):
        """Start audio capture"""
        if self.is_capturing:
            logger.warning("Audio capture already running")
            return
        
        try:
            # Find system audio device if not specified
            if device_id is None:
                device_id = self.find_system_audio_device()
            
            if device_id is None:
                raise ValueError("No suitable audio device found")
            
            # Reset stop event
            self._stop_event.clear()
            
            # Start capture in separate thread
            self.capture_thread = threading.Thread(
                target=self._capture_worker,
                args=(device_id,),
                daemon=True
            )
            
            self.is_capturing = True
            self.capture_thread.start()
            
            logger.info(f"Audio capture started on device {device_id}")
            
        except Exception as e:
            logger.error(f"Error starting audio capture: {e}")
            self.is_capturing = False
            raise
    
    def _capture_worker(self, device_id: int):
        """Worker thread for audio capture"""
        try:
            with sd.InputStream(
                device=device_id,
                channels=self.channels,
                samplerate=self.sample_rate,
                blocksize=self.chunk_size,
                callback=self.audio_callback_internal,
                dtype=np.float32
            ) as stream:
                
                logger.info("Audio stream started")
                
                # Keep capturing until stop event is set
                while not self._stop_event.is_set():
                    time.sleep(0.1)
                
                logger.info("Audio stream stopped")
                
        except Exception as e:
            logger.error(f"Error in audio capture worker: {e}")
        finally:
            self.is_capturing = False
    
    def stop_capture(self):
        """Stop audio capture"""
        if not self.is_capturing:
            logger.warning("Audio capture not running")
            return
        
        try:
            # Signal stop
            self._stop_event.set()
            
            # Wait for thread to finish
            if self.capture_thread and self.capture_thread.is_alive():
                self.capture_thread.join(timeout=2.0)
            
            self.is_capturing = False
            logger.info("Audio capture stopped")
            
        except Exception as e:
            logger.error(f"Error stopping audio capture: {e}")
    
    def set_audio_callback(self, callback: Callable):
        """Set callback for processed audio data"""
        self.audio_callback = callback
    
    def set_speech_detected_callback(self, callback: Callable):
        """Set callback for real-time speech detection"""
        self.speech_detected_callback = callback
    
    def get_statistics(self) -> dict:
        """Get capture statistics"""
        return {
            'is_capturing': self.is_capturing,
            'total_samples': self.total_samples,
            'speech_detected_count': self.speech_detected_count,
            'buffer_size': self.buffer_size,
            'sample_rate': self.sample_rate,
            'channels': self.channels
        }
    
    def test_audio_capture(self, duration: float = 5.0):
        """Test audio capture for specified duration"""
        logger.info(f"Testing audio capture for {duration} seconds...")
        
        captured_data = []
        
        def test_callback(audio_data, features, timestamp):
            captured_data.append({
                'audio': audio_data,
                'features': features,
                'timestamp': timestamp
            })
            logger.info(f"Captured audio segment: {len(audio_data)} samples, RMS: {features.get('rms_energy', 0):.4f}")
        
        # Set test callback
        original_callback = self.audio_callback
        self.set_audio_callback(test_callback)
        
        try:
            # Start capture
            self.start_capture()
            
            # Wait for test duration
            time.sleep(duration)
            
            # Stop capture
            self.stop_capture()
            
            logger.info(f"Audio capture test completed. Captured {len(captured_data)} segments.")
            return captured_data
            
        except Exception as e:
            logger.error(f"Error in audio capture test: {e}")
            return []
        finally:
            # Restore original callback
            self.audio_callback = original_callback
