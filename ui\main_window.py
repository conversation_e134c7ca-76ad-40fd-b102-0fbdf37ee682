"""
Main window for the Human-like Audio AI Desktop Application
"""
import sys
import logging
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                            QWidget, QPushButton, QTextEdit, QLabel, QProgressBar,
                            QTabWidget, QScrollArea, QFrame, QSplitter, QGroupBox,
                            QCheckBox, QSlider, QComboBox, QSpinBox)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QThread, pyqtSlot
from PyQt6.QtGui import QFont, QIcon, QPalette, QColor
from typing import Dict, Any, Optional
from config import Config
from conversation_widget import ConversationWidget

logger = logging.getLogger(__name__)

class MainWindow(QMainWindow):
    """Main application window"""
    
    # Signals
    start_listening_signal = pyqtSignal()
    stop_listening_signal = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        
        # Window properties
        self.setWindowTitle(f"{Config.APP_NAME} v{Config.VERSION}")
        self.setGeometry(100, 100, Config.WINDOW_WIDTH, Config.WINDOW_HEIGHT)
        
        # Application state
        self.is_listening = False
        self.is_speaking = False
        
        # Statistics
        self.stats = {
            "total_interactions": 0,
            "session_start_time": None,
            "audio_level": 0.0,
            "last_transcription": "",
            "last_response": ""
        }
        
        # Setup UI
        self.setup_ui()
        self.setup_theme()
        self.setup_timers()
        
        logger.info("Main window initialized")
    
    def setup_ui(self):
        """Setup the user interface"""
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QHBoxLayout(central_widget)
        
        # Create splitter for resizable panels
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # Left panel - Controls and status
        left_panel = self.create_control_panel()
        splitter.addWidget(left_panel)
        
        # Right panel - Conversation and logs
        right_panel = self.create_conversation_panel()
        splitter.addWidget(right_panel)
        
        # Set splitter proportions
        splitter.setSizes([300, 500])
    
    def create_control_panel(self) -> QWidget:
        """Create the control panel"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Main controls group
        controls_group = QGroupBox("Audio Controls")
        controls_layout = QVBoxLayout(controls_group)
        
        # Listen button
        self.listen_button = QPushButton("🎤 Start Listening")
        self.listen_button.setMinimumHeight(50)
        self.listen_button.setStyleSheet("""
            QPushButton {
                font-size: 14px;
                font-weight: bold;
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)
        self.listen_button.clicked.connect(self.toggle_listening)
        controls_layout.addWidget(self.listen_button)
        
        # Status indicators
        self.status_label = QLabel("Status: Ready")
        self.status_label.setStyleSheet("font-weight: bold; color: #333;")
        controls_layout.addWidget(self.status_label)
        
        # Audio level indicator
        audio_level_layout = QHBoxLayout()
        audio_level_layout.addWidget(QLabel("Audio Level:"))
        self.audio_level_bar = QProgressBar()
        self.audio_level_bar.setRange(0, 100)
        self.audio_level_bar.setValue(0)
        audio_level_layout.addWidget(self.audio_level_bar)
        controls_layout.addLayout(audio_level_layout)
        
        layout.addWidget(controls_group)
        
        # Settings group
        settings_group = QGroupBox("Settings")
        settings_layout = QVBoxLayout(settings_group)
        
        # TTS enabled checkbox
        self.tts_checkbox = QCheckBox("Enable Text-to-Speech")
        self.tts_checkbox.setChecked(Config.TTS_ENABLED)
        settings_layout.addWidget(self.tts_checkbox)
        
        # Learning enabled checkbox
        self.learning_checkbox = QCheckBox("Enable Learning")
        self.learning_checkbox.setChecked(Config.LEARNING_ENABLED)
        settings_layout.addWidget(self.learning_checkbox)
        
        # Response style selection
        style_layout = QHBoxLayout()
        style_layout.addWidget(QLabel("Response Style:"))
        self.style_combo = QComboBox()
        self.style_combo.addItems(["Auto", "Empathetic", "Humorous", "Supportive", "Analytical"])
        style_layout.addWidget(self.style_combo)
        settings_layout.addLayout(style_layout)
        
        # Volume control
        volume_layout = QHBoxLayout()
        volume_layout.addWidget(QLabel("Volume:"))
        self.volume_slider = QSlider(Qt.Orientation.Horizontal)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(int(Config.TTS_VOLUME * 100))
        volume_layout.addWidget(self.volume_slider)
        settings_layout.addLayout(volume_layout)
        
        layout.addWidget(settings_group)
        
        # Statistics group
        stats_group = QGroupBox("Session Statistics")
        stats_layout = QVBoxLayout(stats_group)
        
        self.interactions_label = QLabel("Interactions: 0")
        self.session_time_label = QLabel("Session Time: 00:00:00")
        self.learning_score_label = QLabel("Learning Score: N/A")
        
        stats_layout.addWidget(self.interactions_label)
        stats_layout.addWidget(self.session_time_label)
        stats_layout.addWidget(self.learning_score_label)
        
        layout.addWidget(stats_group)
        
        # Add stretch to push everything to top
        layout.addStretch()
        
        return panel
    
    def create_conversation_panel(self) -> QWidget:
        """Create the conversation panel"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Tab widget for different views
        self.tab_widget = QTabWidget()
        
        # Conversation tab
        self.conversation_widget = ConversationWidget()
        self.tab_widget.addTab(self.conversation_widget, "💬 Conversation")
        
        # Logs tab
        self.logs_widget = QTextEdit()
        self.logs_widget.setReadOnly(True)
        self.logs_widget.setFont(QFont("Consolas", 9))
        self.tab_widget.addTab(self.logs_widget, "📋 Logs")
        
        # Learning insights tab
        self.insights_widget = QTextEdit()
        self.insights_widget.setReadOnly(True)
        self.tab_widget.addTab(self.insights_widget, "🧠 Learning Insights")
        
        layout.addWidget(self.tab_widget)
        
        return panel
    
    def setup_theme(self):
        """Setup application theme"""
        if Config.THEME == "dark":
            self.setStyleSheet("""
                QMainWindow {
                    background-color: #2b2b2b;
                    color: #ffffff;
                }
                QGroupBox {
                    font-weight: bold;
                    border: 2px solid #555555;
                    border-radius: 5px;
                    margin-top: 1ex;
                    padding-top: 10px;
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 5px 0 5px;
                }
                QLabel {
                    color: #ffffff;
                }
                QTextEdit {
                    background-color: #3c3c3c;
                    border: 1px solid #555555;
                    color: #ffffff;
                }
                QTabWidget::pane {
                    border: 1px solid #555555;
                }
                QTabBar::tab {
                    background-color: #404040;
                    color: #ffffff;
                    padding: 8px 16px;
                    margin-right: 2px;
                }
                QTabBar::tab:selected {
                    background-color: #4CAF50;
                }
            """)
    
    def setup_timers(self):
        """Setup update timers"""
        # Statistics update timer
        self.stats_timer = QTimer()
        self.stats_timer.timeout.connect(self.update_statistics)
        self.stats_timer.start(1000)  # Update every second
        
        # Audio level update timer
        self.audio_timer = QTimer()
        self.audio_timer.timeout.connect(self.update_audio_level)
        self.audio_timer.start(100)  # Update every 100ms
    
    @pyqtSlot()
    def toggle_listening(self):
        """Toggle listening state"""
        if self.is_listening:
            self.stop_listening()
        else:
            self.start_listening()
    
    def start_listening(self):
        """Start listening to audio"""
        try:
            self.is_listening = True
            self.listen_button.setText("🛑 Stop Listening")
            self.listen_button.setStyleSheet("""
                QPushButton {
                    font-size: 14px;
                    font-weight: bold;
                    background-color: #f44336;
                    color: white;
                    border: none;
                    border-radius: 5px;
                }
                QPushButton:hover {
                    background-color: #da190b;
                }
            """)
            self.status_label.setText("Status: Listening...")
            self.start_listening_signal.emit()
            
            logger.info("Started listening")
            
        except Exception as e:
            logger.error(f"Error starting listening: {e}")
            self.show_error(f"Failed to start listening: {e}")
    
    def stop_listening(self):
        """Stop listening to audio"""
        try:
            self.is_listening = False
            self.listen_button.setText("🎤 Start Listening")
            self.listen_button.setStyleSheet("""
                QPushButton {
                    font-size: 14px;
                    font-weight: bold;
                    background-color: #4CAF50;
                    color: white;
                    border: none;
                    border-radius: 5px;
                }
                QPushButton:hover {
                    background-color: #45a049;
                }
            """)
            self.status_label.setText("Status: Ready")
            self.stop_listening_signal.emit()
            
            logger.info("Stopped listening")
            
        except Exception as e:
            logger.error(f"Error stopping listening: {e}")
    
    def update_statistics(self):
        """Update statistics display"""
        try:
            self.interactions_label.setText(f"Interactions: {self.stats['total_interactions']}")
            
            # Update session time if listening
            if self.is_listening and self.stats['session_start_time']:
                import time
                elapsed = time.time() - self.stats['session_start_time']
                hours = int(elapsed // 3600)
                minutes = int((elapsed % 3600) // 60)
                seconds = int(elapsed % 60)
                self.session_time_label.setText(f"Session Time: {hours:02d}:{minutes:02d}:{seconds:02d}")
            
        except Exception as e:
            logger.error(f"Error updating statistics: {e}")
    
    def update_audio_level(self):
        """Update audio level indicator"""
        try:
            # This will be connected to actual audio level from the capture system
            level = int(self.stats['audio_level'] * 100)
            self.audio_level_bar.setValue(level)
            
        except Exception as e:
            logger.error(f"Error updating audio level: {e}")
    
    def add_conversation_entry(self, user_text: str, ai_response: str, emotion: str = "neutral"):
        """Add entry to conversation display"""
        try:
            self.conversation_widget.add_message("User", user_text, emotion)
            self.conversation_widget.add_message("AI", ai_response, "neutral")
            self.stats['total_interactions'] += 1
            
        except Exception as e:
            logger.error(f"Error adding conversation entry: {e}")
    
    def add_log_entry(self, message: str, level: str = "INFO"):
        """Add entry to logs"""
        try:
            import datetime
            timestamp = datetime.datetime.now().strftime("%H:%M:%S")
            log_entry = f"[{timestamp}] {level}: {message}\n"
            self.logs_widget.append(log_entry)
            
            # Auto-scroll to bottom
            cursor = self.logs_widget.textCursor()
            cursor.movePosition(cursor.MoveOperation.End)
            self.logs_widget.setTextCursor(cursor)
            
        except Exception as e:
            logger.error(f"Error adding log entry: {e}")
    
    def update_learning_insights(self, insights: Dict[str, Any]):
        """Update learning insights display"""
        try:
            insights_text = "Learning Insights:\n\n"
            
            for key, value in insights.items():
                if isinstance(value, dict):
                    insights_text += f"{key.replace('_', ' ').title()}:\n"
                    for sub_key, sub_value in value.items():
                        insights_text += f"  • {sub_key}: {sub_value}\n"
                else:
                    insights_text += f"{key.replace('_', ' ').title()}: {value}\n"
                insights_text += "\n"
            
            self.insights_widget.setText(insights_text)
            
        except Exception as e:
            logger.error(f"Error updating learning insights: {e}")
    
    def show_error(self, message: str):
        """Show error message"""
        self.add_log_entry(message, "ERROR")
        self.status_label.setText(f"Error: {message[:30]}...")
    
    def show_info(self, message: str):
        """Show info message"""
        self.add_log_entry(message, "INFO")
    
    def get_settings(self) -> Dict[str, Any]:
        """Get current UI settings"""
        return {
            "tts_enabled": self.tts_checkbox.isChecked(),
            "learning_enabled": self.learning_checkbox.isChecked(),
            "response_style": self.style_combo.currentText().lower(),
            "volume": self.volume_slider.value() / 100.0
        }
    
    def set_audio_level(self, level: float):
        """Set audio level (called from main app)"""
        self.stats['audio_level'] = level
    
    def set_session_start_time(self, start_time: float):
        """Set session start time"""
        self.stats['session_start_time'] = start_time
    
    def closeEvent(self, event):
        """Handle window close event"""
        try:
            if self.is_listening:
                self.stop_listening()
            
            logger.info("Main window closing")
            event.accept()
            
        except Exception as e:
            logger.error(f"Error closing window: {e}")
            event.accept()
