"""
Quick Ollama test
"""
import requests

print("🦙 Quick Ollama Test")
print("=" * 30)

try:
    print("Testing connection to Ollama...")
    response = requests.get("http://localhost:11434/api/tags", timeout=3)
    print(f"Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        models = data.get('models', [])
        print(f"✅ Ollama is running!")
        print(f"Available models: {len(models)}")
        
        for model in models:
            print(f"  - {model['name']}")
        
        if models:
            print("\n🎉 Ollama is ready to use!")
        else:
            print("\n⚠️  No models installed. Run: ollama pull llama3.2:1b")
    else:
        print(f"❌ Ollama responded with status: {response.status_code}")
        
except requests.exceptions.ConnectionError:
    print("❌ Cannot connect to Ollama")
    print("Make sure Ollama is running:")
    print("1. Open command prompt")
    print("2. Run: ollama serve")
    print("3. Keep that window open")
    
except Exception as e:
    print(f"❌ Error: {e}")

print("\nNext steps:")
print("1. If Ollama is not running: ollama serve")
print("2. If no models: ollama pull llama3.2:1b")
print("3. Then run the audio AI app!")
