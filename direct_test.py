"""
Direct test of OpenAI API
"""
import openai

# Your API key
api_key = "********************************************************************************************************************************************************************"

print("🤖 Testing OpenAI API directly...")

try:
    client = openai.OpenAI(api_key=api_key)
    
    print("✅ OpenAI client created")
    
    response = client.chat.completions.create(
        model="gpt-3.5-turbo",
        messages=[
            {"role": "system", "content": "You are a helpful AI assistant."},
            {"role": "user", "content": "Say hello in a friendly way"}
        ],
        max_tokens=50
    )
    
    ai_response = response.choices[0].message.content
    print(f"✅ API test successful!")
    print(f"AI Response: {ai_response}")
    
    # Test TTS
    print("\n🔊 Testing Text-to-Speech...")
    try:
        import pyttsx3
        engine = pyttsx3.init()
        engine.say(ai_response)
        engine.runAndWait()
        print("✅ TTS test successful!")
    except Exception as e:
        print(f"❌ TTS failed: {e}")
    
    print("\n🎉 Everything is working! You're ready to run the full application!")
    
except Exception as e:
    print(f"❌ API test failed: {e}")
    print("Please check your API key and internet connection")
