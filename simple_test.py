"""
Simple test to run the Audio AI application with available packages
"""
import sys
import os
import logging
import time
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_basic_functionality():
    """Test basic functionality with available packages"""
    print("🚀 Testing Audio AI Application - Basic Version")
    print("=" * 50)
    
    # Test imports
    try:
        print("Testing imports...")
        
        # Core packages
        import numpy as np
        import sounddevice as sd
        from PyQt6.QtWidgets import QApplication
        import pyttsx3
        import openai
        from dotenv import load_dotenv
        
        print("✅ All core packages imported successfully!")
        
        # Test audio devices
        print("\n🎤 Testing audio devices...")
        devices = sd.query_devices()
        print(f"Found {len(devices)} audio devices:")
        
        # Look for system audio devices
        system_devices = []
        for i, device in enumerate(devices):
            device_name = device['name'].lower()
            if any(keyword in device_name for keyword in ['stereo mix', 'loopback', 'what u hear', 'wave out mix']):
                system_devices.append((i, device['name']))
                print(f"  🔊 System Audio Device {i}: {device['name']}")
        
        if not system_devices:
            print("  ⚠️  No system audio devices found. You may need to enable 'Stereo Mix'")
            print("     Go to Sound Settings > Recording > Show Disabled Devices > Enable Stereo Mix")
        
        # Test TTS
        print("\n🔊 Testing Text-to-Speech...")
        try:
            engine = pyttsx3.init()
            voices = engine.getProperty('voices')
            print(f"✅ TTS initialized with {len(voices)} voices available")
            
            # Test speaking (optional)
            response = input("Would you like to test TTS? (y/n): ").lower().strip()
            if response == 'y':
                engine.say("Hello! This is a test of the text to speech system.")
                engine.runAndWait()
                print("✅ TTS test completed")
        except Exception as e:
            print(f"❌ TTS test failed: {e}")
        
        # Test OpenAI API key
        print("\n🤖 Testing OpenAI configuration...")
        load_dotenv()
        api_key = os.getenv('OPENAI_API_KEY')
        if api_key and api_key != 'your_openai_api_key_here':
            print("✅ OpenAI API key found in .env file")
            
            # Test API connection (optional)
            response = input("Would you like to test OpenAI API connection? (y/n): ").lower().strip()
            if response == 'y':
                try:
                    client = openai.OpenAI(api_key=api_key)
                    response = client.chat.completions.create(
                        model="gpt-3.5-turbo",
                        messages=[{"role": "user", "content": "Say hello in one word"}],
                        max_tokens=5
                    )
                    print(f"✅ OpenAI API test successful: {response.choices[0].message.content}")
                except Exception as e:
                    print(f"❌ OpenAI API test failed: {e}")
        else:
            print("❌ OpenAI API key not found or not set")
            print("   Please edit the .env file and add your OpenAI API key")
        
        # Test PyQt6 UI
        print("\n🖥️  Testing UI framework...")
        try:
            app = QApplication([])
            print("✅ PyQt6 application created successfully")
            app.quit()
        except Exception as e:
            print(f"❌ PyQt6 test failed: {e}")
        
        print("\n" + "=" * 50)
        print("🎯 SUMMARY:")
        print("✅ Core functionality is working!")
        print("✅ Audio capture is available")
        print("✅ Text-to-Speech is working")
        print("✅ UI framework is ready")
        
        if api_key and api_key != 'your_openai_api_key_here':
            print("✅ OpenAI integration is configured")
        else:
            print("⚠️  OpenAI API key needs to be configured")
        
        print("\n🚀 You can run a simplified version of the app!")
        print("   The app will work without Whisper (speech recognition)")
        print("   You can add text manually or use a simpler speech recognition")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please install missing packages with: pip install <package_name>")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def create_simple_demo():
    """Create a simple demo application"""
    print("\n" + "=" * 50)
    print("🎮 SIMPLE DEMO")
    print("=" * 50)
    
    try:
        import pyttsx3
        import openai
        from dotenv import load_dotenv
        
        # Load environment
        load_dotenv()
        api_key = os.getenv('OPENAI_API_KEY')
        
        if not api_key or api_key == 'your_openai_api_key_here':
            print("❌ Please set your OpenAI API key in the .env file first")
            return
        
        # Initialize TTS
        tts_engine = pyttsx3.init()
        
        # Initialize OpenAI
        client = openai.OpenAI(api_key=api_key)
        
        print("🤖 Simple AI Chat Demo")
        print("Type 'quit' to exit")
        print("-" * 30)
        
        while True:
            user_input = input("\nYou: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'bye']:
                print("👋 Goodbye!")
                break
            
            if not user_input:
                continue
            
            try:
                # Get AI response
                response = client.chat.completions.create(
                    model="gpt-3.5-turbo",
                    messages=[
                        {"role": "system", "content": "You are a helpful, empathetic AI assistant. Respond naturally and conversationally."},
                        {"role": "user", "content": user_input}
                    ],
                    max_tokens=150,
                    temperature=0.8
                )
                
                ai_response = response.choices[0].message.content
                print(f"AI: {ai_response}")
                
                # Speak response
                speak_response = input("Speak response? (y/n): ").lower().strip()
                if speak_response == 'y':
                    tts_engine.say(ai_response)
                    tts_engine.runAndWait()
                
            except Exception as e:
                print(f"❌ Error: {e}")
    
    except Exception as e:
        print(f"❌ Demo failed: {e}")

if __name__ == "__main__":
    success = test_basic_functionality()
    
    if success:
        run_demo = input("\nWould you like to run a simple AI chat demo? (y/n): ").lower().strip()
        if run_demo == 'y':
            create_simple_demo()
