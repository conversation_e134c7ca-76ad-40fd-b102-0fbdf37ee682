"""
Real Audio Stream Listener with Audio Device Selector
"""
import sys
import os
import logging
import time
import threading
import queue
import numpy as np
import sounddevice as sd
import requests
import json
from pathlib import Path
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                            QWidget, QPushButton, QTextEdit, QLabel, QProgressBar,
                            QComboBox, QGroupBox, QCheckBox, QSpinBox)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QThread, pyqtSlot

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AudioDeviceSelector(QWidget):
    """Widget for selecting audio input device"""
    device_changed = pyqtSignal(int)  # device_id
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.refresh_devices()
    
    def setup_ui(self):
        """Setup device selector UI"""
        layout = QVBoxLayout(self)
        
        # Device selection group
        device_group = QGroupBox("🎧 Audio Input Device")
        device_layout = QVBoxLayout(device_group)
        
        # Device dropdown
        device_select_layout = QHBoxLayout()
        device_select_layout.addWidget(QLabel("Select Device:"))
        
        self.device_combo = QComboBox()
        self.device_combo.currentIndexChanged.connect(self.on_device_changed)
        device_select_layout.addWidget(self.device_combo)
        
        self.refresh_button = QPushButton("🔄 Refresh")
        self.refresh_button.clicked.connect(self.refresh_devices)
        device_select_layout.addWidget(self.refresh_button)
        
        device_layout.addLayout(device_select_layout)
        
        # Device info
        self.device_info_label = QLabel("Select a device to see details")
        self.device_info_label.setStyleSheet("padding: 8px; background-color: #f0f0f0; border-radius: 4px; font-size: 10px;")
        self.device_info_label.setWordWrap(True)
        device_layout.addWidget(self.device_info_label)
        
        layout.addWidget(device_group)
        
        # Audio settings group
        settings_group = QGroupBox("⚙️ Audio Settings")
        settings_layout = QVBoxLayout(settings_group)
        
        # Sample rate
        sample_rate_layout = QHBoxLayout()
        sample_rate_layout.addWidget(QLabel("Sample Rate:"))
        self.sample_rate_combo = QComboBox()
        self.sample_rate_combo.addItems(["44100", "48000", "22050", "16000"])
        self.sample_rate_combo.setCurrentText("44100")
        sample_rate_layout.addWidget(self.sample_rate_combo)
        sample_rate_layout.addWidget(QLabel("Hz"))
        sample_rate_layout.addStretch()
        settings_layout.addLayout(sample_rate_layout)
        
        # Speech threshold
        threshold_layout = QHBoxLayout()
        threshold_layout.addWidget(QLabel("Speech Threshold:"))
        self.threshold_spin = QSpinBox()
        self.threshold_spin.setRange(1, 100)
        self.threshold_spin.setValue(10)
        self.threshold_spin.setSuffix("%")
        threshold_layout.addWidget(self.threshold_spin)
        threshold_layout.addStretch()
        settings_layout.addLayout(threshold_layout)
        
        # Auto-detect speech
        self.auto_detect_checkbox = QCheckBox("Auto-detect speech in audio")
        self.auto_detect_checkbox.setChecked(True)
        settings_layout.addWidget(self.auto_detect_checkbox)
        
        layout.addWidget(settings_group)
    
    def refresh_devices(self):
        """Refresh available audio devices"""
        try:
            self.device_combo.clear()
            devices = sd.query_devices()
            
            for i, device in enumerate(devices):
                # Show input devices only
                if device['max_input_channels'] > 0:
                    device_name = f"{i}: {device['name']}"
                    
                    # Highlight special devices
                    if any(keyword in device['name'].lower() for keyword in 
                           ['analogue', 'analog', 'stereo mix', 'loopback', 'what u hear']):
                        device_name += " ⭐"
                    
                    self.device_combo.addItem(device_name, i)
            
            # Try to auto-select a good device
            self.auto_select_device()
            
        except Exception as e:
            logger.error(f"Error refreshing devices: {e}")
    
    def auto_select_device(self):
        """Auto-select a suitable audio device"""
        try:
            devices = sd.query_devices()
            
            # Priority order for device selection
            priority_keywords = [
                ['analogue', 'analog'],  # User's preference
                ['stereo mix', 'loopback', 'what u hear'],  # System audio
                ['microphone', 'mic'],  # Fallback to mic
            ]
            
            for keywords in priority_keywords:
                for i in range(self.device_combo.count()):
                    device_id = self.device_combo.itemData(i)
                    device_name = devices[device_id]['name'].lower()
                    
                    if any(keyword in device_name for keyword in keywords):
                        self.device_combo.setCurrentIndex(i)
                        logger.info(f"Auto-selected device: {devices[device_id]['name']}")
                        return
            
            # If no priority device found, select first available
            if self.device_combo.count() > 0:
                self.device_combo.setCurrentIndex(0)
                
        except Exception as e:
            logger.error(f"Error auto-selecting device: {e}")
    
    def on_device_changed(self):
        """Handle device selection change"""
        try:
            if self.device_combo.currentIndex() >= 0:
                device_id = self.device_combo.currentData()
                devices = sd.query_devices()
                device = devices[device_id]
                
                # Update device info
                info_text = f"""
Device: {device['name']}
Channels: {device['max_input_channels']} input, {device['max_output_channels']} output
Default Sample Rate: {device['default_samplerate']} Hz
Host API: {sd.query_hostapis()[device['hostapi']]['name']}
                """.strip()
                
                self.device_info_label.setText(info_text)
                
                # Emit signal
                self.device_changed.emit(device_id)
                
        except Exception as e:
            logger.error(f"Error updating device info: {e}")
    
    def get_selected_device(self):
        """Get currently selected device ID"""
        if self.device_combo.currentIndex() >= 0:
            return self.device_combo.currentData()
        return None
    
    def get_sample_rate(self):
        """Get selected sample rate"""
        return int(self.sample_rate_combo.currentText())
    
    def get_speech_threshold(self):
        """Get speech detection threshold"""
        return self.threshold_spin.value() / 1000.0  # Convert percentage to decimal
    
    def is_auto_detect_enabled(self):
        """Check if auto speech detection is enabled"""
        return self.auto_detect_checkbox.isChecked()

class AudioProcessor(QThread):
    """Enhanced audio processor with device selection"""
    speech_detected = pyqtSignal(str)
    audio_level_updated = pyqtSignal(float)
    error_occurred = pyqtSignal(str)
    device_status_updated = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.is_listening = False
        self.device_id = None
        self.sample_rate = 44100
        self.speech_threshold = 0.01
        self.auto_detect = True
        self.audio_buffer = []
        self.last_speech_time = 0
        self.silence_duration = 2.0
    
    def configure(self, device_id, sample_rate, speech_threshold, auto_detect):
        """Configure audio processor settings"""
        self.device_id = device_id
        self.sample_rate = sample_rate
        self.speech_threshold = speech_threshold
        self.auto_detect = auto_detect
        
        logger.info(f"Audio processor configured: Device {device_id}, {sample_rate}Hz, threshold {speech_threshold}")
    
    def start_listening(self):
        """Start listening to selected audio device"""
        if self.device_id is None:
            self.error_occurred.emit("No audio device selected")
            return
        
        self.is_listening = True
        self.start()
    
    def stop_listening(self):
        """Stop listening"""
        self.is_listening = False
        if self.isRunning():
            self.wait(3000)  # Wait up to 3 seconds
    
    def audio_callback(self, indata, frames, time, status):
        """Audio input callback"""
        if status:
            logger.warning(f"Audio callback status: {status}")
        
        try:
            # Convert to mono if needed
            if len(indata.shape) > 1 and indata.shape[1] > 1:
                audio_data = np.mean(indata, axis=1)
            else:
                audio_data = indata.flatten()
            
            # Calculate audio level
            rms = np.sqrt(np.mean(audio_data ** 2))
            self.audio_level_updated.emit(float(rms))
            
            # Speech detection if enabled
            if self.auto_detect and rms > self.speech_threshold:
                self.last_speech_time = time.currentTime
                self.audio_buffer.extend(audio_data)
            
            # Check for end of speech
            current_time = time.currentTime
            if (self.last_speech_time > 0 and 
                current_time - self.last_speech_time > self.silence_duration and
                len(self.audio_buffer) > 0):
                
                self.process_audio_buffer()
                self.audio_buffer = []
                self.last_speech_time = 0
                
        except Exception as e:
            logger.error(f"Audio callback error: {e}")
    
    def process_audio_buffer(self):
        """Process accumulated audio buffer"""
        try:
            if len(self.audio_buffer) < self.sample_rate * 0.5:  # Less than 0.5 seconds
                return
            
            audio_array = np.array(self.audio_buffer, dtype=np.float32)
            text = self.transcribe_audio(audio_array)
            
            if text and len(text.strip()) > 3:
                self.speech_detected.emit(text.strip())
                
        except Exception as e:
            logger.error(f"Error processing audio buffer: {e}")
    
    def transcribe_audio(self, audio_data):
        """Transcribe audio using available methods"""
        try:
            import speech_recognition as sr
            
            # Convert to 16-bit PCM
            audio_int16 = (audio_data * 32767).astype(np.int16)
            audio_bytes = audio_int16.tobytes()
            
            # Create recognizer and audio data
            r = sr.Recognizer()
            audio_data_obj = sr.AudioData(audio_bytes, self.sample_rate, 2)
            
            # Recognize speech
            text = r.recognize_google(audio_data_obj, language='en-US')
            logger.info(f"Transcribed: {text}")
            return text
            
        except ImportError:
            return "[Speech detected - install SpeechRecognition for transcription]"
        except Exception as e:
            logger.debug(f"Speech recognition failed: {e}")
            return "[Speech detected but could not transcribe]"
    
    def run(self):
        """Main audio processing loop"""
        try:
            devices = sd.query_devices()
            device = devices[self.device_id]
            
            self.device_status_updated.emit(f"Using: {device['name']}")
            
            # Determine number of channels
            channels = min(device['max_input_channels'], 2)  # Use up to 2 channels
            
            logger.info(f"Starting audio stream: Device {self.device_id}, {channels} channels, {self.sample_rate}Hz")
            
            with sd.InputStream(
                device=self.device_id,
                channels=channels,
                samplerate=self.sample_rate,
                blocksize=1024,
                callback=self.audio_callback,
                dtype=np.float32
            ):
                logger.info("Audio stream started successfully")
                
                while self.is_listening:
                    time.sleep(0.1)
                
                logger.info("Audio stream stopped")
                
        except Exception as e:
            error_msg = f"Audio stream error: {e}"
            logger.error(error_msg)
            self.error_occurred.emit(error_msg)

class OllamaAI(QThread):
    """Ollama AI processor (same as before)"""
    response_ready = pyqtSignal(str, str)
    error_occurred = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.model_name = "llama3.2:1b"
        self.request_queue = queue.Queue()
        self.running = True
    
    def add_request(self, text):
        self.request_queue.put(text)
    
    def run(self):
        while self.running:
            try:
                text = self.request_queue.get(timeout=1.0)
                self.process_text(text)
            except queue.Empty:
                continue
    
    def process_text(self, text):
        try:
            response = requests.post(
                "http://localhost:11434/api/generate",
                json={
                    "model": self.model_name,
                    "prompt": f"Someone just said: '{text}'. Respond naturally and conversationally as if you're listening to their conversation. Keep it brief (1-2 sentences).",
                    "stream": False,
                    "options": {"temperature": 0.8, "max_tokens": 80}
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                response_text = data.get("response", "").strip()
                if response_text.startswith("Assistant:"):
                    response_text = response_text[10:].strip()
                self.response_ready.emit(text, response_text)
            else:
                self.error_occurred.emit(f"Ollama API error: {response.status_code}")
                
        except Exception as e:
            self.error_occurred.emit(f"AI error: {e}")
    
    def stop(self):
        self.running = False

class EnhancedAudioListenerWindow(QMainWindow):
    """Enhanced main window with device selector"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🎧 Enhanced Audio Stream Listener - Device Selector")
        self.setGeometry(100, 100, 1000, 800)
        
        self.audio_processor = None
        self.ollama_ai = None
        self.tts_engine = None
        self.is_listening = False
        
        self.setup_ui()
        self.setup_components()
    
    def setup_ui(self):
        """Setup enhanced user interface"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QHBoxLayout(central_widget)
        
        # Left panel - Device selector and controls
        left_panel = QWidget()
        left_panel.setMaximumWidth(350)
        left_layout = QVBoxLayout(left_panel)
        
        # Title
        title = QLabel("🎧 Audio Stream Listener")
        title.setStyleSheet("font-size: 16px; font-weight: bold; padding: 10px; text-align: center; background-color: #2196F3; color: white; border-radius: 8px;")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        left_layout.addWidget(title)
        
        # Device selector
        self.device_selector = AudioDeviceSelector()
        self.device_selector.device_changed.connect(self.on_device_changed)
        left_layout.addWidget(self.device_selector)
        
        # Controls
        controls_group = QGroupBox("🎮 Controls")
        controls_layout = QVBoxLayout(controls_group)
        
        self.listen_button = QPushButton("🎤 Start Listening")
        self.listen_button.setMinimumHeight(50)
        self.listen_button.setStyleSheet("""
            QPushButton {
                font-size: 14px; font-weight: bold; background-color: #4CAF50; color: white; 
                border: none; border-radius: 8px; padding: 10px;
            }
            QPushButton:hover { background-color: #45a049; }
        """)
        self.listen_button.clicked.connect(self.toggle_listening)
        controls_layout.addWidget(self.listen_button)
        
        self.status_label = QLabel("Status: Ready")
        self.status_label.setStyleSheet("padding: 8px; background-color: #f0f0f0; border-radius: 4px; font-weight: bold;")
        controls_layout.addWidget(self.status_label)
        
        self.device_status_label = QLabel("Device: None selected")
        self.device_status_label.setStyleSheet("padding: 8px; background-color: #e8f4fd; border-radius: 4px; font-size: 10px;")
        self.device_status_label.setWordWrap(True)
        controls_layout.addWidget(self.device_status_label)
        
        left_layout.addWidget(controls_group)
        
        # Audio level
        audio_group = QGroupBox("📊 Audio Level")
        audio_layout = QVBoxLayout(audio_group)
        
        self.audio_level_bar = QProgressBar()
        self.audio_level_bar.setRange(0, 100)
        self.audio_level_bar.setValue(0)
        self.audio_level_bar.setStyleSheet("QProgressBar::chunk { background-color: #4CAF50; }")
        audio_layout.addWidget(self.audio_level_bar)
        
        self.audio_level_label = QLabel("0%")
        self.audio_level_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        audio_layout.addWidget(self.audio_level_label)
        
        left_layout.addWidget(audio_group)
        left_layout.addStretch()
        
        main_layout.addWidget(left_panel)
        
        # Right panel - Conversation
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        conv_title = QLabel("💬 Live Audio Conversation")
        conv_title.setStyleSheet("font-size: 14px; font-weight: bold; padding: 8px; background-color: #4CAF50; color: white; border-radius: 6px;")
        conv_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        right_layout.addWidget(conv_title)
        
        self.conversation_area = QTextEdit()
        self.conversation_area.setReadOnly(True)
        self.conversation_area.setStyleSheet("""
            QTextEdit {
                font-size: 12px; padding: 15px; background-color: #fafafa;
                border: 2px solid #ddd; border-radius: 10px;
            }
        """)
        right_layout.addWidget(self.conversation_area)
        
        main_layout.addWidget(right_panel)
    
    def setup_components(self):
        """Setup audio and AI components"""
        try:
            import pyttsx3
            self.tts_engine = pyttsx3.init()
        except Exception as e:
            logger.error(f"TTS setup failed: {e}")
        
        self.audio_processor = AudioProcessor()
        self.audio_processor.speech_detected.connect(self.on_speech_detected)
        self.audio_processor.audio_level_updated.connect(self.on_audio_level_updated)
        self.audio_processor.error_occurred.connect(self.on_error)
        self.audio_processor.device_status_updated.connect(self.on_device_status_updated)
        
        self.ollama_ai = OllamaAI()
        self.ollama_ai.response_ready.connect(self.on_ai_response)
        self.ollama_ai.error_occurred.connect(self.on_error)
        self.ollama_ai.start()
    
    def on_device_changed(self, device_id):
        """Handle device selection change"""
        self.device_status_label.setText(f"Device: Selected ID {device_id}")
    
    def toggle_listening(self):
        if self.is_listening:
            self.stop_listening()
        else:
            self.start_listening()
    
    def start_listening(self):
        try:
            device_id = self.device_selector.get_selected_device()
            if device_id is None:
                self.on_error("Please select an audio device first")
                return
            
            # Configure audio processor
            self.audio_processor.configure(
                device_id=device_id,
                sample_rate=self.device_selector.get_sample_rate(),
                speech_threshold=self.device_selector.get_speech_threshold(),
                auto_detect=self.device_selector.is_auto_detect_enabled()
            )
            
            self.audio_processor.start_listening()
            self.is_listening = True
            self.listen_button.setText("🛑 Stop Listening")
            self.listen_button.setStyleSheet("""
                QPushButton {
                    font-size: 14px; font-weight: bold; background-color: #f44336; color: white; 
                    border: none; border-radius: 8px; padding: 10px;
                }
                QPushButton:hover { background-color: #da190b; }
            """)
            self.status_label.setText("Status: 🎧 Listening...")
            self.add_message("System", f"🎧 Started listening to selected audio device!", "#4CAF50")
            
        except Exception as e:
            self.on_error(f"Failed to start listening: {e}")
    
    def stop_listening(self):
        try:
            self.audio_processor.stop_listening()
            self.is_listening = False
            self.listen_button.setText("🎤 Start Listening")
            self.listen_button.setStyleSheet("""
                QPushButton {
                    font-size: 14px; font-weight: bold; background-color: #4CAF50; color: white; 
                    border: none; border-radius: 8px; padding: 10px;
                }
                QPushButton:hover { background-color: #45a049; }
            """)
            self.status_label.setText("Status: Ready")
            self.add_message("System", "🛑 Stopped listening", "#FF5722")
            
        except Exception as e:
            self.on_error(f"Failed to stop listening: {e}")
    
    @pyqtSlot(str)
    def on_speech_detected(self, text):
        self.add_message("🎧 Heard", text, "#2196F3")
        if self.ollama_ai:
            self.ollama_ai.add_request(text)
    
    @pyqtSlot(str, str)
    def on_ai_response(self, input_text, ai_response):
        self.add_message("🤖 AI", ai_response, "#4CAF50")
        if self.tts_engine:
            try:
                self.tts_engine.say(ai_response)
                self.tts_engine.runAndWait()
            except Exception as e:
                logger.error(f"TTS error: {e}")
    
    @pyqtSlot(float)
    def on_audio_level_updated(self, level):
        level_percent = int(level * 1000)
        self.audio_level_bar.setValue(min(100, level_percent))
        self.audio_level_label.setText(f"{level_percent}%")
    
    @pyqtSlot(str)
    def on_device_status_updated(self, status):
        self.device_status_label.setText(f"Device: {status}")
    
    @pyqtSlot(str)
    def on_error(self, error_message):
        self.add_message("❌ Error", error_message, "#F44336")
        self.status_label.setText(f"Status: Error - {error_message[:30]}...")
    
    def add_message(self, sender: str, message: str, color: str):
        timestamp = time.strftime("%H:%M:%S")
        formatted_message = f"""
        <div style="margin: 8px 0; padding: 12px; border-left: 4px solid {color}; background-color: #f9f9f9; border-radius: 8px;">
            <strong style="color: {color};">[{timestamp}] {sender}:</strong><br>
            <span style="margin-left: 10px; color: #333;">{message}</span>
        </div>
        """
        self.conversation_area.append(formatted_message)
        cursor = self.conversation_area.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.conversation_area.setTextCursor(cursor)
    
    def closeEvent(self, event):
        if self.is_listening:
            self.stop_listening()
        if self.ollama_ai:
            self.ollama_ai.stop()
            self.ollama_ai.wait()
        event.accept()

def main():
    app = QApplication(sys.argv)
    
    window = EnhancedAudioListenerWindow()
    window.show()
    
    window.add_message("System", "🎉 Enhanced Audio Stream Listener Ready!", "#4CAF50")
    window.add_message("System", "1. Select your audio device (look for 'Analogue 1+2' ⭐)", "#2196F3")
    window.add_message("System", "2. Adjust settings if needed", "#FF9800")
    window.add_message("System", "3. Click 'Start Listening' to begin!", "#9C27B0")
    
    return app.exec()

if __name__ == "__main__":
    print("🎧 Starting Enhanced Audio Stream Listener with Device Selector...")
    sys.exit(main())
