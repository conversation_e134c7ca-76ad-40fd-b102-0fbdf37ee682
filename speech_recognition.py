"""
Speech recognition module using OpenAI Whisper
"""
import whisper
import numpy as np
import torch
import logging
import threading
import time
from typing import Dict, Any, Optional, Tuple
from pathlib import Path
import tempfile
import soundfile as sf
from config import Config
from utils import normalize_audio

logger = logging.getLogger(__name__)

class WhisperSpeechRecognizer:
    """Speech recognition using OpenAI Whisper"""
    
    def __init__(self, model_name: str = Config.WHISPER_MODEL):
        self.model_name = model_name
        self.model = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        # Processing queue
        self.processing_queue = []
        self.processing_lock = threading.Lock()
        self.is_processing = False
        
        # Statistics
        self.total_transcriptions = 0
        self.successful_transcriptions = 0
        self.average_confidence = 0.0
        
        logger.info(f"Whisper recognizer initialized with model: {model_name}, device: {self.device}")
    
    def load_model(self):
        """Load Whisper model"""
        try:
            if self.model is None:
                logger.info(f"Loading Whisper model: {self.model_name}")
                self.model = whisper.load_model(self.model_name, device=self.device)
                logger.info("Whisper model loaded successfully")
            return True
        except Exception as e:
            logger.error(f"Error loading Whisper model: {e}")
            return False
    
    def preprocess_audio(self, audio_data: np.ndarray, sample_rate: int) -> np.ndarray:
        """Preprocess audio for Whisper"""
        try:
            # Normalize audio
            audio_data = normalize_audio(audio_data)
            
            # Resample to 16kHz if needed (Whisper expects 16kHz)
            if sample_rate != 16000:
                # Simple resampling (for production, use proper resampling)
                target_length = int(len(audio_data) * 16000 / sample_rate)
                audio_data = np.interp(
                    np.linspace(0, len(audio_data), target_length),
                    np.arange(len(audio_data)),
                    audio_data
                )
            
            # Ensure audio is at least 1 second long
            min_length = 16000  # 1 second at 16kHz
            if len(audio_data) < min_length:
                audio_data = np.pad(audio_data, (0, min_length - len(audio_data)))
            
            return audio_data.astype(np.float32)
            
        except Exception as e:
            logger.error(f"Error preprocessing audio: {e}")
            return audio_data
    
    def detect_language(self, audio_data: np.ndarray) -> Tuple[str, float]:
        """Detect language of audio"""
        try:
            if self.model is None:
                if not self.load_model():
                    return "en", 0.0
            
            # Detect language
            audio_tensor = torch.from_numpy(audio_data).to(self.device)
            _, probs = self.model.detect_language(audio_tensor)
            
            # Get most likely language
            detected_language = max(probs, key=probs.get)
            confidence = probs[detected_language]
            
            logger.debug(f"Detected language: {detected_language} (confidence: {confidence:.3f})")
            return detected_language, confidence
            
        except Exception as e:
            logger.error(f"Error detecting language: {e}")
            return "en", 0.0
    
    def transcribe_audio(self, 
                        audio_data: np.ndarray, 
                        sample_rate: int = Config.SAMPLE_RATE,
                        language: Optional[str] = None) -> Dict[str, Any]:
        """Transcribe audio to text"""
        try:
            if self.model is None:
                if not self.load_model():
                    return {"text": "", "confidence": 0.0, "error": "Model not loaded"}
            
            # Preprocess audio
            processed_audio = self.preprocess_audio(audio_data, sample_rate)
            
            # Check if audio has sufficient energy
            rms = np.sqrt(np.mean(processed_audio ** 2))
            if rms < Config.SPEECH_THRESHOLD:
                return {"text": "", "confidence": 0.0, "error": "Insufficient audio energy"}
            
            # Detect language if not specified
            if language is None:
                language, lang_confidence = self.detect_language(processed_audio)
                if lang_confidence < 0.5:
                    language = "en"  # Default to English
            
            # Transcribe
            start_time = time.time()
            result = self.model.transcribe(
                processed_audio,
                language=language,
                task="transcribe",
                fp16=False,
                verbose=False
            )
            
            transcription_time = time.time() - start_time
            
            # Extract results
            text = result.get("text", "").strip()
            segments = result.get("segments", [])
            
            # Calculate average confidence from segments
            if segments:
                avg_confidence = np.mean([
                    segment.get("avg_logprob", 0.0) 
                    for segment in segments
                ])
                # Convert log probability to confidence (approximate)
                confidence = min(1.0, max(0.0, (avg_confidence + 1.0)))
            else:
                confidence = 0.5 if text else 0.0
            
            # Update statistics
            self.total_transcriptions += 1
            if text:
                self.successful_transcriptions += 1
                self.average_confidence = (
                    (self.average_confidence * (self.successful_transcriptions - 1) + confidence) 
                    / self.successful_transcriptions
                )
            
            result_dict = {
                "text": text,
                "confidence": confidence,
                "language": language,
                "segments": segments,
                "transcription_time": transcription_time,
                "audio_duration": len(processed_audio) / 16000,
                "error": None
            }
            
            logger.debug(f"Transcription: '{text}' (confidence: {confidence:.3f}, time: {transcription_time:.2f}s)")
            return result_dict
            
        except Exception as e:
            logger.error(f"Error transcribing audio: {e}")
            return {"text": "", "confidence": 0.0, "error": str(e)}
    
    def transcribe_file(self, file_path: str, language: Optional[str] = None) -> Dict[str, Any]:
        """Transcribe audio file"""
        try:
            # Load audio file
            audio_data, sample_rate = sf.read(file_path)
            
            # Convert to mono if stereo
            if len(audio_data.shape) > 1:
                audio_data = np.mean(audio_data, axis=1)
            
            return self.transcribe_audio(audio_data, sample_rate, language)
            
        except Exception as e:
            logger.error(f"Error transcribing file {file_path}: {e}")
            return {"text": "", "confidence": 0.0, "error": str(e)}
    
    def save_audio_for_transcription(self, audio_data: np.ndarray, sample_rate: int) -> str:
        """Save audio data to temporary file for transcription"""
        try:
            # Create temporary file
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                temp_path = temp_file.name
            
            # Save audio
            sf.write(temp_path, audio_data, sample_rate)
            return temp_path
            
        except Exception as e:
            logger.error(f"Error saving audio for transcription: {e}")
            return ""
    
    def batch_transcribe(self, audio_segments: list) -> list:
        """Transcribe multiple audio segments"""
        results = []
        
        for i, (audio_data, sample_rate) in enumerate(audio_segments):
            logger.info(f"Transcribing segment {i+1}/{len(audio_segments)}")
            result = self.transcribe_audio(audio_data, sample_rate)
            results.append(result)
        
        return results
    
    def is_speech_detected(self, audio_data: np.ndarray, sample_rate: int) -> bool:
        """Quick check if audio contains speech"""
        try:
            # Simple energy-based detection
            rms = np.sqrt(np.mean(audio_data ** 2))
            if rms < Config.SPEECH_THRESHOLD:
                return False
            
            # Check spectral characteristics
            fft = np.fft.fft(audio_data)
            magnitude = np.abs(fft)
            
            # Speech typically has energy in 300-3400 Hz range
            freqs = np.fft.fftfreq(len(fft), 1/sample_rate)
            speech_band = (freqs >= 300) & (freqs <= 3400)
            speech_energy = np.sum(magnitude[speech_band])
            total_energy = np.sum(magnitude)
            
            speech_ratio = speech_energy / total_energy if total_energy > 0 else 0
            
            # Consider it speech if significant energy in speech band
            return speech_ratio > 0.1
            
        except Exception as e:
            logger.error(f"Error in speech detection: {e}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get transcription statistics"""
        success_rate = (
            self.successful_transcriptions / self.total_transcriptions 
            if self.total_transcriptions > 0 else 0
        )
        
        return {
            "total_transcriptions": self.total_transcriptions,
            "successful_transcriptions": self.successful_transcriptions,
            "success_rate": success_rate,
            "average_confidence": self.average_confidence,
            "model_name": self.model_name,
            "device": self.device,
            "model_loaded": self.model is not None
        }
    
    def cleanup(self):
        """Cleanup resources"""
        try:
            if self.model is not None:
                del self.model
                self.model = None
                
            # Clear CUDA cache if using GPU
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                
            logger.info("Whisper recognizer cleaned up")
            
        except Exception as e:
            logger.error(f"Error cleaning up Whisper recognizer: {e}")
    
    def test_transcription(self, test_text: str = "Hello, this is a test of the speech recognition system."):
        """Test transcription with synthetic audio (for development)"""
        logger.info("Testing speech recognition...")
        
        # For testing, we'll create a simple sine wave
        # In practice, you'd use real audio
        duration = 3.0
        sample_rate = 16000
        frequency = 440  # A4 note
        
        t = np.linspace(0, duration, int(sample_rate * duration))
        test_audio = 0.1 * np.sin(2 * np.pi * frequency * t)
        
        result = self.transcribe_audio(test_audio, sample_rate)
        logger.info(f"Test transcription result: {result}")
        
        return result
