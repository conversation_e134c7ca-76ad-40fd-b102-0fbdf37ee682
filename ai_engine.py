"""
AI Engine for generating human-like responses using OpenAI GPT
"""
import openai
import logging
import time
import json
from typing import Dict, List, Any, Optional
from config import Config, PersonalityConfig
from nlp_processor import NLPProcessor

logger = logging.getLogger(__name__)

class AIResponseEngine:
    """Generates human-like responses using OpenAI GPT"""
    
    def __init__(self):
        # Initialize OpenAI client
        if Config.OPENAI_API_KEY:
            openai.api_key = Config.OPENAI_API_KEY
        else:
            logger.warning("OpenAI API key not found. AI responses will be limited.")
        
        self.nlp_processor = NLPProcessor()
        
        # Response generation settings
        self.model = Config.GPT_MODEL
        self.max_tokens = Config.MAX_TOKENS
        self.temperature = Config.TEMPERATURE
        
        # Personality and context
        self.personality_traits = {
            "empathy": PersonalityConfig.EMPATHY_LEVEL,
            "humor": PersonalityConfig.HUMOR_LEVEL,
            "curiosity": PersonalityConfig.CURIOSITY_LEVEL
        }
        
        # Conversation memory
        self.conversation_history = []
        self.user_context = {}
        
        # Response templates
        self.response_templates = {
            "empathetic": [
                "I can understand how you might feel about that.",
                "That sounds like it could be {emotion} for you.",
                "I hear you, and I want you to know that your feelings are valid.",
                "It seems like this is really important to you."
            ],
            "humorous": [
                "Well, that's one way to look at it! 😄",
                "Life has a funny way of surprising us, doesn't it?",
                "I'd probably react the same way - maybe with more coffee though! ☕",
                "That reminds me of something that would happen in a sitcom!"
            ],
            "supportive": [
                "You're handling this really well.",
                "I believe in your ability to work through this.",
                "You've got this! Sometimes we just need someone to remind us.",
                "It's okay to feel this way. You're not alone in this."
            ],
            "curious": [
                "That's fascinating! Tell me more about {topic}.",
                "I'm really interested in hearing your perspective on this.",
                "What made you think about that?",
                "How did you come to that conclusion?"
            ]
        }
        
        logger.info("AI Response Engine initialized")
    
    def generate_system_prompt(self, context: Dict[str, Any]) -> str:
        """Generate system prompt based on context"""
        user_emotion = context.get("emotion", {}).get("emotion", "neutral")
        user_intent = context.get("intent", {}).get("intent", "statement")
        response_style = context.get("response_style", "neutral")
        
        base_prompt = f"""You are a highly empathetic and intelligent AI assistant that listens to audio from the user's environment and responds in the most human-like way possible. 

Your personality traits:
- Empathy level: {self.personality_traits['empathy']}/1.0
- Humor level: {self.personality_traits['humor']}/1.0  
- Curiosity level: {self.personality_traits['curiosity']}/1.0

Current context:
- User's detected emotion: {user_emotion}
- User's intent: {user_intent}
- Recommended response style: {response_style}

Guidelines:
1. Respond naturally and conversationally, as a caring friend would
2. Show genuine interest and empathy
3. Use appropriate humor when the situation calls for it
4. Ask follow-up questions to show engagement
5. Acknowledge the user's emotions and validate their feelings
6. Keep responses concise but meaningful (1-3 sentences typically)
7. Use emojis sparingly and appropriately
8. Adapt your tone to match the user's emotional state

Remember: You're not just providing information - you're being a supportive, understanding companion."""

        return base_prompt
    
    def generate_response(self, 
                         user_input: str, 
                         context: Dict[str, Any],
                         conversation_history: List[Dict] = None) -> Dict[str, Any]:
        """Generate AI response to user input"""
        try:
            start_time = time.time()
            
            # Analyze user input if not already done
            if "analysis" not in context:
                context["analysis"] = self.nlp_processor.analyze_text_comprehensive(user_input)
            
            # Determine response style
            response_style = self.nlp_processor.get_response_style_recommendation(context["analysis"])
            context["response_style"] = response_style
            
            # Generate response using OpenAI
            if Config.OPENAI_API_KEY:
                response = self._generate_openai_response(user_input, context, conversation_history)
            else:
                response = self._generate_fallback_response(user_input, context)
            
            # Post-process response
            processed_response = self._post_process_response(response, context)
            
            generation_time = time.time() - start_time
            
            result = {
                "response": processed_response,
                "response_style": response_style,
                "generation_time": generation_time,
                "context_used": context,
                "confidence": 0.8 if Config.OPENAI_API_KEY else 0.6
            }
            
            # Update conversation history
            self._update_conversation_history(user_input, processed_response, context)
            
            logger.debug(f"Generated response in {generation_time:.2f}s: '{processed_response[:50]}...'")
            return result
            
        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return {
                "response": "I'm sorry, I'm having trouble processing that right now. Could you try again?",
                "response_style": "apologetic",
                "generation_time": 0,
                "error": str(e),
                "confidence": 0.1
            }
    
    def _generate_openai_response(self, 
                                 user_input: str, 
                                 context: Dict[str, Any],
                                 conversation_history: List[Dict] = None) -> str:
        """Generate response using OpenAI GPT"""
        try:
            # Build messages for chat completion
            messages = []
            
            # System prompt
            system_prompt = self.generate_system_prompt(context)
            messages.append({"role": "system", "content": system_prompt})
            
            # Add conversation history
            if conversation_history:
                for entry in conversation_history[-5:]:  # Last 5 exchanges
                    messages.append({"role": "user", "content": entry.get("user_input", "")})
                    messages.append({"role": "assistant", "content": entry.get("ai_response", "")})
            
            # Add current user input
            messages.append({"role": "user", "content": user_input})
            
            # Generate response
            response = openai.ChatCompletion.create(
                model=self.model,
                messages=messages,
                max_tokens=self.max_tokens,
                temperature=self.temperature,
                presence_penalty=0.1,
                frequency_penalty=0.1
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"Error with OpenAI API: {e}")
            return self._generate_fallback_response(user_input, context)
    
    def _generate_fallback_response(self, user_input: str, context: Dict[str, Any]) -> str:
        """Generate fallback response when OpenAI is not available"""
        try:
            analysis = context.get("analysis", {})
            emotion = analysis.get("emotion", {}).get("emotion", "neutral")
            intent = analysis.get("intent", {}).get("intent", "statement")
            response_style = context.get("response_style", "neutral")
            
            # Use emotion-based responses from config
            if emotion in PersonalityConfig.EMOTION_MAPPING:
                base_response = PersonalityConfig.EMOTION_MAPPING[emotion]
            else:
                base_response = "I'm listening and here to chat with you."
            
            # Add style-specific elements
            if response_style in self.response_templates:
                template = self.response_templates[response_style][0]  # Use first template
                if "{emotion}" in template:
                    template = template.replace("{emotion}", emotion)
                if "{topic}" in template:
                    topics = analysis.get("topics", ["that"])
                    template = template.replace("{topic}", topics[0] if topics else "that")
                
                # Combine base response with template
                response = f"{base_response} {template}"
            else:
                response = base_response
            
            # Add follow-up based on intent
            if intent == "question":
                response += " What would you like to know more about?"
            elif intent == "complaint":
                response += " Is there anything specific I can help you with?"
            elif intent == "greeting":
                response = "Hello! I'm here and ready to chat. How are you doing today?"
            
            return response
            
        except Exception as e:
            logger.error(f"Error generating fallback response: {e}")
            return "I'm here and listening. Please tell me more."
    
    def _post_process_response(self, response: str, context: Dict[str, Any]) -> str:
        """Post-process the generated response"""
        try:
            # Ensure response isn't too long
            if len(response) > 300:
                sentences = response.split('. ')
                response = '. '.join(sentences[:2]) + '.'
            
            # Add personality touches based on context
            emotion = context.get("analysis", {}).get("emotion", {}).get("emotion", "neutral")
            
            # Add appropriate emotional responses
            if emotion == "sad" and "sorry" not in response.lower():
                response = response.rstrip('.') + ". I'm here for you. 💙"
            elif emotion == "happy" and not any(emoji in response for emoji in ["😊", "🙂", "😄"]):
                response = response.rstrip('.') + ". 😊"
            elif emotion == "excited" and "!" not in response:
                response = response.rstrip('.') + "!"
            
            return response.strip()
            
        except Exception as e:
            logger.error(f"Error post-processing response: {e}")
            return response
    
    def _update_conversation_history(self, user_input: str, ai_response: str, context: Dict[str, Any]):
        """Update conversation history"""
        try:
            entry = {
                "timestamp": time.time(),
                "user_input": user_input,
                "ai_response": ai_response,
                "context": context
            }
            
            self.conversation_history.append(entry)
            
            # Keep only recent history
            max_history = 20
            if len(self.conversation_history) > max_history:
                self.conversation_history = self.conversation_history[-max_history:]
            
        except Exception as e:
            logger.error(f"Error updating conversation history: {e}")
    
    def get_conversation_summary(self) -> Dict[str, Any]:
        """Get summary of conversation"""
        try:
            if not self.conversation_history:
                return {"summary": "No conversation history"}
            
            total_exchanges = len(self.conversation_history)
            recent_emotions = []
            response_styles = []
            
            for entry in self.conversation_history[-10:]:
                context = entry.get("context", {})
                analysis = context.get("analysis", {})
                
                emotion = analysis.get("emotion", {}).get("emotion")
                if emotion:
                    recent_emotions.append(emotion)
                
                style = context.get("response_style")
                if style:
                    response_styles.append(style)
            
            # Most common recent emotion
            emotion_counts = {}
            for emotion in recent_emotions:
                emotion_counts[emotion] = emotion_counts.get(emotion, 0) + 1
            
            dominant_emotion = max(emotion_counts, key=emotion_counts.get) if emotion_counts else "neutral"
            
            return {
                "total_exchanges": total_exchanges,
                "dominant_emotion": dominant_emotion,
                "response_styles_used": list(set(response_styles)),
                "conversation_active": True
            }
            
        except Exception as e:
            logger.error(f"Error getting conversation summary: {e}")
            return {"error": str(e)}
    
    def adjust_personality(self, trait: str, value: float):
        """Adjust personality trait"""
        if trait in self.personality_traits and 0 <= value <= 1:
            self.personality_traits[trait] = value
            logger.info(f"Adjusted {trait} to {value}")
    
    def clear_conversation_history(self):
        """Clear conversation history"""
        self.conversation_history.clear()
        logger.info("Conversation history cleared")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get AI engine statistics"""
        return {
            "total_responses_generated": len(self.conversation_history),
            "personality_traits": self.personality_traits,
            "model_used": self.model,
            "openai_available": bool(Config.OPENAI_API_KEY),
            "conversation_length": len(self.conversation_history)
        }
