"""
Test Ollama AI integration
"""
import requests
import time

def test_ollama():
    """Test if Ollama is available and working"""
    print("🦙 Testing Ollama AI (Free Local AI)")
    print("=" * 50)
    
    # Check if Ollama is running
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print("✅ Ollama is running!")
            
            # List available models
            data = response.json()
            models = [model['name'] for model in data.get('models', [])]
            
            if models:
                print(f"✅ Available models: {', '.join(models)}")
                
                # Test chat with first available model
                test_model = models[0]
                print(f"\n🤖 Testing chat with {test_model}...")
                
                chat_response = requests.post(
                    "http://localhost:11434/api/generate",
                    json={
                        "model": test_model,
                        "prompt": "Say hello in a friendly way (one sentence only)",
                        "stream": False
                    },
                    timeout=30
                )
                
                if chat_response.status_code == 200:
                    result = chat_response.json()
                    ai_response = result.get("response", "").strip()
                    print(f"✅ AI Response: {ai_response}")
                    
                    # Test TTS
                    print("\n🔊 Testing Text-to-Speech...")
                    try:
                        import pyttsx3
                        engine = pyttsx3.init()
                        engine.say(ai_response)
                        engine.runAndWait()
                        print("✅ TTS test successful!")
                    except Exception as e:
                        print(f"❌ TTS failed: {e}")
                    
                    print("\n🎉 Ollama is working perfectly!")
                    print("🚀 You can now run the Audio AI app with free local AI!")
                    return True
                else:
                    print(f"❌ Chat test failed: {chat_response.status_code}")
                    return False
            else:
                print("❌ No models available. Please pull a model first.")
                print("\nTo install a model, run:")
                print("  ollama pull llama3.2:1b")
                return False
        else:
            print(f"❌ Ollama responded with status: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Ollama is not running or not installed")
        print("\n🚀 SETUP INSTRUCTIONS:")
        print("1. Download Ollama from: https://ollama.ai")
        print("2. Install and run Ollama")
        print("3. Open command prompt and run:")
        print("   ollama pull llama3.2:1b")
        print("4. Start Ollama service:")
        print("   ollama serve")
        print("5. Run this test again")
        return False
    except Exception as e:
        print(f"❌ Error testing Ollama: {e}")
        return False

def create_ollama_demo():
    """Create a simple demo with Ollama"""
    print("\n" + "=" * 50)
    print("🎮 OLLAMA AI CHAT DEMO")
    print("=" * 50)
    
    try:
        # Check available models
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code != 200:
            print("❌ Ollama not available")
            return
        
        data = response.json()
        models = [model['name'] for model in data.get('models', [])]
        
        if not models:
            print("❌ No models available")
            return
        
        model = models[0]
        print(f"🤖 Using model: {model}")
        print("Type 'quit' to exit")
        print("-" * 30)
        
        # Initialize TTS
        try:
            import pyttsx3
            tts_engine = pyttsx3.init()
            tts_available = True
        except:
            tts_available = False
            print("⚠️  TTS not available")
        
        while True:
            user_input = input("\nYou: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'bye']:
                print("👋 Goodbye!")
                break
            
            if not user_input:
                continue
            
            try:
                print("🤔 Thinking...")
                
                # Get AI response
                response = requests.post(
                    "http://localhost:11434/api/generate",
                    json={
                        "model": model,
                        "prompt": f"You are a helpful, empathetic AI assistant. Respond naturally and conversationally to: {user_input}",
                        "stream": False,
                        "options": {
                            "temperature": 0.8,
                            "max_tokens": 100
                        }
                    },
                    timeout=30
                )
                
                if response.status_code == 200:
                    result = response.json()
                    ai_response = result.get("response", "").strip()
                    
                    # Clean up response
                    if ai_response.startswith("Assistant:"):
                        ai_response = ai_response[10:].strip()
                    
                    print(f"AI: {ai_response}")
                    
                    # Speak response
                    if tts_available:
                        speak_response = input("Speak response? (y/n): ").lower().strip()
                        if speak_response == 'y':
                            tts_engine.say(ai_response)
                            tts_engine.runAndWait()
                else:
                    print(f"❌ Error: {response.status_code}")
                
            except Exception as e:
                print(f"❌ Error: {e}")
    
    except Exception as e:
        print(f"❌ Demo failed: {e}")

if __name__ == "__main__":
    success = test_ollama()
    
    if success:
        run_demo = input("\nWould you like to run the Ollama chat demo? (y/n): ").lower().strip()
        if run_demo == 'y':
            create_ollama_demo()
    else:
        print("\n💡 Alternative: You can also use other free AI services:")
        print("   - Hugging Face Transformers (local)")
        print("   - Google Colab (free GPU)")
        print("   - Anthropic Claude (free tier)")
        print("   - Google Gemini (free tier)")
