# Human-like Audio AI Desktop Application

A sophisticated desktop application that continuously listens to system audio, analyzes spoken content in real-time, and responds with human-like AI interaction featuring empathy, humor, and continuous learning capabilities.

## Features

### 🎤 **System Audio Capture**
- Captures desktop/system audio instead of microphone input
- Real-time audio processing and speech detection
- Automatic silence detection and audio segmentation

### 🗣️ **Advanced Speech Recognition**
- OpenAI Whisper integration for high-accuracy transcription
- Multi-language support with automatic detection
- Real-time speech-to-text conversion

### 🧠 **Natural Language Understanding**
- Sentiment analysis and emotion detection
- Intent recognition and context understanding
- Topic extraction and conversation tracking

### 🤖 **Human-like AI Responses**
- OpenAI GPT integration for natural conversations
- Emotional intelligence and empathy
- Context-aware responses with personality traits
- Humor and supportive communication styles

### 🔊 **Text-to-Speech**
- Natural voice synthesis with emotional tone matching
- Adjustable speech rate and volume
- Multiple voice options

### 📚 **Machine Learning & Personalization**
- Continuous learning from every interaction
- User preference tracking and adaptation
- Response quality improvement over time
- Personalized conversation styles

### 🖥️ **User Interface**
- Clean, modern PyQt6 interface
- Real-time conversation display
- Learning progress visualization
- Audio level monitoring and controls

## Installation

### Prerequisites

1. **Python 3.8+** (recommended: Python 3.10+)
2. **OpenAI API Key** (required for AI responses)
3. **System Audio Loopback** (Windows: Stereo Mix, macOS: Soundflower, Linux: PulseAudio)

### Setup Instructions

1. **Clone or download the project:**
   ```bash
   git clone <repository-url>
   cd AUGMENT
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables:**
   Create a `.env` file in the project root:
   ```env
   OPENAI_API_KEY=your_openai_api_key_here
   ```

4. **Enable system audio capture:**
   
   **Windows:**
   - Right-click on speaker icon → "Open Sound settings"
   - Click "Sound Control Panel" → "Recording" tab
   - Right-click and "Show Disabled Devices"
   - Enable "Stereo Mix" or "What U Hear"
   
   **macOS:**
   - Install Soundflower or BlackHole
   - Set as input device in Audio MIDI Setup
   
   **Linux:**
   - Configure PulseAudio loopback module

5. **Run the application:**
   ```bash
   python main.py
   ```

## Usage

### Getting Started

1. **Launch the application** by running `python main.py`
2. **Click "Start Listening"** to begin audio capture
3. **Play audio or speak** - the AI will detect speech and respond
4. **View conversations** in the main chat interface
5. **Monitor learning progress** in the Learning Insights tab

### Configuration Options

- **Response Style**: Choose between Auto, Empathetic, Humorous, Supportive, or Analytical
- **Text-to-Speech**: Enable/disable voice responses
- **Learning**: Enable/disable continuous learning
- **Volume Control**: Adjust TTS volume

### Audio Sources

The application can listen to:
- Music and podcasts
- Video calls and meetings
- Streaming content
- Any system audio output

## Architecture

### Core Components

- **`main.py`**: Application orchestration and UI integration
- **`audio_capture.py`**: System audio capture using sounddevice
- **`speech_recognition.py`**: OpenAI Whisper integration
- **`nlp_processor.py`**: Sentiment analysis and NLP
- **`ai_engine.py`**: OpenAI GPT response generation
- **`tts_engine.py`**: Text-to-speech synthesis
- **`learning_system.py`**: Machine learning and personalization
- **`database.py`**: SQLite data persistence
- **`ui/`**: PyQt6 user interface components

### Data Flow

1. **Audio Capture** → System audio is captured in real-time
2. **Speech Detection** → Audio is analyzed for speech content
3. **Transcription** → Whisper converts speech to text
4. **NLP Analysis** → Text is analyzed for sentiment, emotion, intent
5. **AI Response** → GPT generates contextual, human-like response
6. **Learning** → Interaction is recorded for continuous improvement
7. **Output** → Response is displayed and optionally spoken

## Learning System

The application continuously learns and improves through:

- **Interaction Recording**: Every conversation is stored and analyzed
- **User Preference Learning**: Response styles and topics are tracked
- **Context Similarity**: Similar past conversations inform responses
- **Feedback Integration**: Response quality is monitored and improved
- **Personalization**: Communication style adapts to user preferences

## Troubleshooting

### Common Issues

**"No system audio device found"**
- Ensure system audio loopback is enabled
- Check audio device permissions
- Try running as administrator (Windows)

**"OpenAI API key not found"**
- Set the OPENAI_API_KEY environment variable
- Check the .env file is in the project root

**"Whisper model loading failed"**
- Ensure sufficient disk space (models are 100MB-1GB)
- Check internet connection for initial download
- Try a smaller model (base instead of large)

**Poor transcription quality**
- Increase audio volume
- Reduce background noise
- Check audio device quality

### Performance Optimization

- Use GPU acceleration if available (CUDA)
- Adjust chunk size for your system performance
- Limit conversation history for memory efficiency
- Use smaller Whisper models for faster processing

## Configuration

### Environment Variables

```env
OPENAI_API_KEY=your_api_key_here
```

### Config Settings

Edit `config.py` to customize:

- Audio capture settings (sample rate, channels)
- Whisper model size (base, small, medium, large)
- GPT model and parameters
- TTS settings
- Learning system parameters

## Development

### Adding New Features

1. **Audio Processing**: Extend `audio_capture.py`
2. **NLP Features**: Add to `nlp_processor.py`
3. **AI Responses**: Modify `ai_engine.py`
4. **Learning**: Enhance `learning_system.py`
5. **UI Components**: Add to `ui/` directory

### Testing

```bash
# Test individual components
python -c "from audio_capture import SystemAudioCapture; SystemAudioCapture().test_audio_capture()"
python -c "from speech_recognition import WhisperSpeechRecognizer; WhisperSpeechRecognizer().test_transcription()"
python -c "from tts_engine import TextToSpeechEngine; TextToSpeechEngine().test_speech()"
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Support

For issues and questions:
- Check the troubleshooting section
- Review the logs in `logs/app.log`
- Open an issue on the repository

## Acknowledgments

- OpenAI for Whisper and GPT APIs
- Hugging Face for NLP models
- PyQt6 for the user interface
- The open-source community for various libraries

---

**Note**: This application requires an OpenAI API key and system audio loopback capability. Ensure you have the necessary permissions and setup before running.
