"""
Desktop Audio Output Listener - Captures what's playing on your computer
"""
import sys
import os
import logging
import time
import threading
import queue
import numpy as np
import sounddevice as sd
import requests
import json
from pathlib import Path
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                            QWidget, QPushButton, QTextEdit, QLabel, QProgressBar,
                            QComboBox, QGroupBox, QCheckBox, QSpinBox, QSplitter,
                            QScrollArea, QFrame)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QThread, pyqtSlot
from PyQt6.QtGui import QFont

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DesktopAudioCapture(QThread):
    """Captures desktop audio output (what's playing on speakers)"""
    audio_detected = pyqtSignal(str, float)  # text, confidence
    audio_level_updated = pyqtSignal(float)  # audio level
    raw_audio_data = pyqtSignal(str)  # raw transcribed text for display
    error_occurred = pyqtSignal(str)
    status_updated = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.is_listening = False
        self.sample_rate = 44100
        self.chunk_size = 1024
        self.speech_threshold = 0.005  # Lower threshold for desktop audio
        self.silence_duration = 3.0  # Longer silence for desktop audio
        self.audio_buffer = []
        self.last_speech_time = 0
        self.device_id = None
        
    def find_desktop_audio_device(self):
        """Find desktop audio output device (loopback/stereo mix)"""
        try:
            devices = sd.query_devices()
            
            # Look for desktop audio capture devices
            desktop_keywords = [
                'stereo mix', 'loopback', 'what u hear', 'wave out mix', 
                'speakers', 'desktop audio', 'system audio', 'output'
            ]
            
            desktop_devices = []
            
            for i, device in enumerate(devices):
                device_name = device['name'].lower()
                
                # Check if it's an input device with desktop audio keywords
                if (device['max_input_channels'] > 0 and 
                    any(keyword in device_name for keyword in desktop_keywords)):
                    desktop_devices.append((i, device['name'], device))
                    logger.info(f"Found desktop audio device: {device['name']}")
            
            if desktop_devices:
                # Prefer "Stereo Mix" or "Loopback" if available
                for device_id, name, device in desktop_devices:
                    if any(preferred in name.lower() for preferred in ['stereo mix', 'loopback']):
                        return device_id, name
                
                # Otherwise return the first one found
                return desktop_devices[0][0], desktop_devices[0][1]
            
            # If no specific desktop audio device, look for any input device that might work
            self.status_updated.emit("No desktop audio device found. You may need to enable 'Stereo Mix'")
            return None, None
            
        except Exception as e:
            logger.error(f"Error finding desktop audio device: {e}")
            return None, None
    
    def start_listening(self):
        """Start listening to desktop audio"""
        self.device_id, device_name = self.find_desktop_audio_device()
        
        if self.device_id is None:
            self.error_occurred.emit(
                "No desktop audio device found!\n\n"
                "To enable desktop audio capture:\n"
                "1. Right-click speaker icon in taskbar\n"
                "2. Open Sound settings → Sound Control Panel\n"
                "3. Recording tab → Right-click → Show Disabled Devices\n"
                "4. Enable 'Stereo Mix' or 'What U Hear'\n"
                "5. Set it as default recording device"
            )
            return False
        
        self.status_updated.emit(f"Using desktop audio device: {device_name}")
        self.is_listening = True
        self.start()
        return True
    
    def stop_listening(self):
        """Stop listening"""
        self.is_listening = False
        if self.isRunning():
            self.wait(3000)
    
    def audio_callback(self, indata, frames, time, status):
        """Process incoming desktop audio"""
        if status:
            logger.warning(f"Audio callback status: {status}")
        
        try:
            # Convert to mono
            if len(indata.shape) > 1 and indata.shape[1] > 1:
                audio_data = np.mean(indata, axis=1)
            else:
                audio_data = indata.flatten()
            
            # Calculate audio level
            rms = np.sqrt(np.mean(audio_data ** 2))
            self.audio_level_updated.emit(float(rms))
            
            # Detect speech/audio activity
            if rms > self.speech_threshold:
                self.last_speech_time = time.currentTime
                self.audio_buffer.extend(audio_data)
                
                # Limit buffer size to prevent memory issues
                max_buffer_size = self.sample_rate * 30  # 30 seconds max
                if len(self.audio_buffer) > max_buffer_size:
                    self.audio_buffer = self.audio_buffer[-max_buffer_size:]
            
            # Check for end of speech/audio segment
            current_time = time.currentTime
            if (self.last_speech_time > 0 and 
                current_time - self.last_speech_time > self.silence_duration and
                len(self.audio_buffer) > 0):
                
                self.process_audio_segment()
                self.audio_buffer = []
                self.last_speech_time = 0
                
        except Exception as e:
            logger.error(f"Audio callback error: {e}")
    
    def process_audio_segment(self):
        """Process accumulated audio segment"""
        try:
            if len(self.audio_buffer) < self.sample_rate * 1.0:  # Less than 1 second
                return
            
            audio_array = np.array(self.audio_buffer, dtype=np.float32)
            
            # Transcribe the audio
            text, confidence = self.transcribe_audio(audio_array)
            
            if text and len(text.strip()) > 2:
                # Emit raw audio data for display
                self.raw_audio_data.emit(text.strip())
                
                # Only send to AI if it seems like meaningful speech
                if confidence > 0.3 and len(text.strip()) > 5:
                    self.audio_detected.emit(text.strip(), confidence)
                
        except Exception as e:
            logger.error(f"Error processing audio segment: {e}")
    
    def transcribe_audio(self, audio_data):
        """Transcribe audio using available methods"""
        try:
            import speech_recognition as sr
            
            # Convert to 16-bit PCM
            audio_int16 = (audio_data * 32767).astype(np.int16)
            audio_bytes = audio_int16.tobytes()
            
            # Create recognizer
            r = sr.Recognizer()
            r.energy_threshold = 300  # Lower threshold for desktop audio
            r.dynamic_energy_threshold = True
            
            # Create audio data object
            audio_data_obj = sr.AudioData(audio_bytes, self.sample_rate, 2)
            
            # Try to recognize speech
            try:
                text = r.recognize_google(audio_data_obj, language='en-US')
                confidence = 0.8  # Assume good confidence for Google
                logger.info(f"Transcribed desktop audio: {text}")
                return text, confidence
            except sr.UnknownValueError:
                # Could not understand audio
                return "[Audio detected but no clear speech]", 0.1
            except sr.RequestError as e:
                logger.error(f"Google Speech Recognition error: {e}")
                return "[Speech recognition service error]", 0.0
                
        except ImportError:
            return "[Install SpeechRecognition for transcription]", 0.0
        except Exception as e:
            logger.error(f"Transcription error: {e}")
            return f"[Transcription error: {e}]", 0.0
    
    def run(self):
        """Main audio capture loop"""
        try:
            devices = sd.query_devices()
            device = devices[self.device_id]
            
            logger.info(f"Starting desktop audio capture: {device['name']}")
            
            # Use stereo if available
            channels = min(device['max_input_channels'], 2)
            
            with sd.InputStream(
                device=self.device_id,
                channels=channels,
                samplerate=self.sample_rate,
                blocksize=self.chunk_size,
                callback=self.audio_callback,
                dtype=np.float32
            ):
                self.status_updated.emit(f"Listening to: {device['name']}")
                logger.info("Desktop audio capture started")
                
                while self.is_listening:
                    time.sleep(0.1)
                
                logger.info("Desktop audio capture stopped")
                
        except Exception as e:
            error_msg = f"Desktop audio capture error: {e}"
            logger.error(error_msg)
            self.error_occurred.emit(error_msg)

class OllamaAI(QThread):
    """AI processor for responding to desktop audio"""
    response_ready = pyqtSignal(str, str, str)  # original_text, ai_response, response_type
    error_occurred = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.model_name = "llama3.2:1b"
        self.request_queue = queue.Queue()
        self.running = True
        self.conversation_context = []
    
    def add_request(self, text, confidence):
        """Add text for AI processing"""
        self.request_queue.put((text, confidence))
    
    def run(self):
        """Main AI processing loop"""
        while self.running:
            try:
                text, confidence = self.request_queue.get(timeout=1.0)
                self.process_text(text, confidence)
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"AI processing error: {e}")
    
    def process_text(self, text, confidence):
        """Process text with AI and generate contextual response"""
        try:
            # Check if Ollama is available
            response = requests.get("http://localhost:11434/api/tags", timeout=3)
            if response.status_code != 200:
                # Fallback response if Ollama not available
                self.generate_fallback_response(text)
                return
            
            # Build context from recent conversation
            context = ""
            if self.conversation_context:
                recent_context = self.conversation_context[-3:]  # Last 3 exchanges
                context = "Recent context: " + " | ".join([item['text'] for item in recent_context]) + "\n\n"
            
            # Create contextual prompt
            prompt = f"""{context}You are listening to desktop audio (YouTube, music, podcasts, etc.). Someone just said or discussed: "{text}"

Respond naturally as if you're having a conversation about what you heard. You can:
- Comment on the topic being discussed
- Ask follow-up questions
- Share related thoughts or insights
- React to the content appropriately

Keep your response conversational and brief (1-2 sentences). Respond as if you're genuinely interested in the topic."""
            
            # Generate AI response
            ai_response = requests.post(
                "http://localhost:11434/api/generate",
                json={
                    "model": self.model_name,
                    "prompt": prompt,
                    "stream": False,
                    "options": {
                        "temperature": 0.8,
                        "max_tokens": 100
                    }
                },
                timeout=30
            )
            
            if ai_response.status_code == 200:
                data = ai_response.json()
                response_text = data.get("response", "").strip()
                
                # Clean response
                if response_text.startswith("Assistant:"):
                    response_text = response_text[10:].strip()
                
                # Determine response type based on content
                response_type = self.classify_response(text, response_text)
                
                # Add to conversation context
                self.conversation_context.append({
                    'text': text,
                    'response': response_text,
                    'timestamp': time.time()
                })
                
                # Keep context manageable
                if len(self.conversation_context) > 10:
                    self.conversation_context = self.conversation_context[-10:]
                
                self.response_ready.emit(text, response_text, response_type)
            else:
                self.error_occurred.emit(f"Ollama API error: {ai_response.status_code}")
                
        except Exception as e:
            logger.error(f"AI processing error: {e}")
            self.generate_fallback_response(text)
    
    def classify_response(self, original_text, response_text):
        """Classify the type of response for better display"""
        response_lower = response_text.lower()
        original_lower = original_text.lower()
        
        if any(word in response_lower for word in ['?', 'what', 'how', 'why', 'when', 'where']):
            return "question"
        elif any(word in response_lower for word in ['interesting', 'fascinating', 'wow', 'amazing']):
            return "commentary"
        elif any(word in original_lower for word in ['music', 'song', 'artist', 'album']):
            return "music"
        elif any(word in original_lower for word in ['news', 'politics', 'world', 'country']):
            return "news"
        else:
            return "general"
    
    def generate_fallback_response(self, text):
        """Generate fallback response when Ollama is not available"""
        fallback_responses = [
            "That's interesting! I'd love to hear more about that topic.",
            "I find that fascinating. What do you think about it?",
            "That's a great point. It really makes you think.",
            "Interesting perspective! I hadn't considered that angle.",
            "That's worth discussing further. What's your take on it?"
        ]
        
        import random
        response = random.choice(fallback_responses)
        self.response_ready.emit(text, response, "fallback")
    
    def stop(self):
        """Stop AI processing"""
        self.running = False

class DesktopAudioListenerWindow(QMainWindow):
    """Main window for desktop audio listening"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🖥️ Desktop Audio Listener - AI Responds to What You're Hearing")
        self.setGeometry(100, 100, 1200, 800)
        
        self.audio_capture = None
        self.ollama_ai = None
        self.tts_engine = None
        self.is_listening = False
        
        self.setup_ui()
        self.setup_components()
    
    def setup_ui(self):
        """Setup user interface"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main splitter
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        central_widget_layout = QVBoxLayout(central_widget)
        central_widget_layout.addWidget(main_splitter)
        
        # Left panel - Controls and status
        left_panel = QWidget()
        left_panel.setMaximumWidth(350)
        left_layout = QVBoxLayout(left_panel)
        
        # Title
        title = QLabel("🖥️ Desktop Audio Listener")
        title.setStyleSheet("""
            font-size: 18px; font-weight: bold; padding: 15px; 
            background-color: #2196F3; color: white; border-radius: 10px;
            text-align: center;
        """)
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        left_layout.addWidget(title)
        
        # Controls group
        controls_group = QGroupBox("🎮 Controls")
        controls_layout = QVBoxLayout(controls_group)
        
        self.listen_button = QPushButton("🎧 Start Listening to Desktop Audio")
        self.listen_button.setMinimumHeight(60)
        self.listen_button.setStyleSheet("""
            QPushButton {
                font-size: 14px; font-weight: bold; background-color: #4CAF50; 
                color: white; border: none; border-radius: 10px; padding: 15px;
            }
            QPushButton:hover { background-color: #45a049; }
        """)
        self.listen_button.clicked.connect(self.toggle_listening)
        controls_layout.addWidget(self.listen_button)
        
        self.status_label = QLabel("Status: Ready to listen to desktop audio")
        self.status_label.setStyleSheet("""
            padding: 10px; background-color: #f0f0f0; border-radius: 8px; 
            font-weight: bold; border: 2px solid #ddd;
        """)
        self.status_label.setWordWrap(True)
        controls_layout.addWidget(self.status_label)
        
        left_layout.addWidget(controls_group)
        
        # Audio level group
        audio_group = QGroupBox("📊 Desktop Audio Level")
        audio_layout = QVBoxLayout(audio_group)
        
        self.audio_level_bar = QProgressBar()
        self.audio_level_bar.setRange(0, 100)
        self.audio_level_bar.setValue(0)
        self.audio_level_bar.setStyleSheet("""
            QProgressBar { border: 2px solid #ddd; border-radius: 8px; text-align: center; }
            QProgressBar::chunk { background-color: #4CAF50; border-radius: 6px; }
        """)
        audio_layout.addWidget(self.audio_level_bar)
        
        self.audio_level_label = QLabel("0% - No audio detected")
        self.audio_level_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.audio_level_label.setStyleSheet("padding: 5px; font-weight: bold;")
        audio_layout.addWidget(self.audio_level_label)
        
        left_layout.addWidget(audio_group)
        
        # Info group
        info_group = QGroupBox("ℹ️ How It Works")
        info_layout = QVBoxLayout(info_group)
        
        info_text = QLabel("""
This app listens to your desktop audio output:
• YouTube videos
• Music and podcasts  
• Live streams
• Any audio playing on your computer

The AI will respond to speech it hears in the audio!
        """.strip())
        info_text.setStyleSheet("padding: 10px; background-color: #e8f4fd; border-radius: 6px;")
        info_text.setWordWrap(True)
        info_layout.addWidget(info_text)
        
        left_layout.addWidget(info_group)
        left_layout.addStretch()
        
        main_splitter.addWidget(left_panel)
        
        # Right panel - Audio display and conversation
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        # Heard audio section
        heard_group = QGroupBox("🎧 What I'm Hearing from Desktop Audio")
        heard_layout = QVBoxLayout(heard_group)
        
        self.heard_audio_area = QTextEdit()
        self.heard_audio_area.setReadOnly(True)
        self.heard_audio_area.setMaximumHeight(200)
        self.heard_audio_area.setStyleSheet("""
            QTextEdit {
                font-size: 11px; padding: 10px; background-color: #f8f9fa;
                border: 2px solid #e9ecef; border-radius: 8px;
                font-family: 'Courier New', monospace;
            }
        """)
        self.heard_audio_area.setPlaceholderText("Desktop audio transcription will appear here...")
        heard_layout.addWidget(self.heard_audio_area)
        
        right_layout.addWidget(heard_group)
        
        # AI conversation section
        conversation_group = QGroupBox("🤖 AI Responses to Desktop Audio")
        conversation_layout = QVBoxLayout(conversation_group)
        
        self.conversation_area = QTextEdit()
        self.conversation_area.setReadOnly(True)
        self.conversation_area.setStyleSheet("""
            QTextEdit {
                font-size: 12px; padding: 15px; background-color: #fafafa;
                border: 2px solid #ddd; border-radius: 10px;
            }
        """)
        conversation_layout.addWidget(self.conversation_area)
        
        right_layout.addWidget(conversation_group)
        
        main_splitter.addWidget(right_panel)
        
        # Set splitter proportions
        main_splitter.setSizes([350, 850])
    
    def setup_components(self):
        """Setup audio capture and AI components"""
        # Setup TTS
        try:
            import pyttsx3
            self.tts_engine = pyttsx3.init()
            self.tts_engine.setProperty('rate', 180)  # Slightly slower for better clarity
        except Exception as e:
            logger.error(f"TTS setup failed: {e}")
        
        # Setup audio capture
        self.audio_capture = DesktopAudioCapture()
        self.audio_capture.audio_detected.connect(self.on_audio_detected)
        self.audio_capture.raw_audio_data.connect(self.on_raw_audio_data)
        self.audio_capture.audio_level_updated.connect(self.on_audio_level_updated)
        self.audio_capture.error_occurred.connect(self.on_error)
        self.audio_capture.status_updated.connect(self.on_status_updated)
        
        # Setup AI
        self.ollama_ai = OllamaAI()
        self.ollama_ai.response_ready.connect(self.on_ai_response)
        self.ollama_ai.error_occurred.connect(self.on_error)
        self.ollama_ai.start()
    
    def toggle_listening(self):
        """Toggle desktop audio listening"""
        if self.is_listening:
            self.stop_listening()
        else:
            self.start_listening()
    
    def start_listening(self):
        """Start listening to desktop audio"""
        try:
            if self.audio_capture.start_listening():
                self.is_listening = True
                self.listen_button.setText("🛑 Stop Listening")
                self.listen_button.setStyleSheet("""
                    QPushButton {
                        font-size: 14px; font-weight: bold; background-color: #f44336; 
                        color: white; border: none; border-radius: 10px; padding: 15px;
                    }
                    QPushButton:hover { background-color: #da190b; }
                """)
                self.add_conversation_message("System", "🎧 Started listening to desktop audio output!", "#4CAF50")
            
        except Exception as e:
            self.on_error(f"Failed to start listening: {e}")
    
    def stop_listening(self):
        """Stop listening to desktop audio"""
        try:
            self.audio_capture.stop_listening()
            self.is_listening = False
            self.listen_button.setText("🎧 Start Listening to Desktop Audio")
            self.listen_button.setStyleSheet("""
                QPushButton {
                    font-size: 14px; font-weight: bold; background-color: #4CAF50; 
                    color: white; border: none; border-radius: 10px; padding: 15px;
                }
                QPushButton:hover { background-color: #45a049; }
            """)
            self.status_label.setText("Status: Ready to listen to desktop audio")
            self.add_conversation_message("System", "🛑 Stopped listening to desktop audio", "#FF5722")
            
        except Exception as e:
            self.on_error(f"Failed to stop listening: {e}")
    
    @pyqtSlot(str, float)
    def on_audio_detected(self, text, confidence):
        """Handle detected audio that should trigger AI response"""
        self.add_conversation_message("🎧 Heard", text, "#2196F3")
        if self.ollama_ai:
            self.ollama_ai.add_request(text, confidence)
    
    @pyqtSlot(str)
    def on_raw_audio_data(self, text):
        """Display all heard audio in the raw audio area"""
        timestamp = time.strftime("%H:%M:%S")
        self.heard_audio_area.append(f"[{timestamp}] {text}")
        
        # Auto-scroll to bottom
        cursor = self.heard_audio_area.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.heard_audio_area.setTextCursor(cursor)
    
    @pyqtSlot(str, str, str)
    def on_ai_response(self, original_text, ai_response, response_type):
        """Handle AI response"""
        # Choose emoji based on response type
        emoji_map = {
            "question": "❓",
            "commentary": "💭", 
            "music": "🎵",
            "news": "📰",
            "general": "🤖",
            "fallback": "💡"
        }
        
        emoji = emoji_map.get(response_type, "🤖")
        self.add_conversation_message(f"{emoji} AI", ai_response, "#4CAF50")
        
        # Speak response if TTS is available
        if self.tts_engine:
            try:
                self.tts_engine.say(ai_response)
                self.tts_engine.runAndWait()
            except Exception as e:
                logger.error(f"TTS error: {e}")
    
    @pyqtSlot(float)
    def on_audio_level_updated(self, level):
        """Update audio level display"""
        level_percent = int(level * 2000)  # Scale for desktop audio
        level_percent = min(100, level_percent)
        self.audio_level_bar.setValue(level_percent)
        
        if level_percent > 5:
            self.audio_level_label.setText(f"{level_percent}% - Desktop audio detected")
        else:
            self.audio_level_label.setText(f"{level_percent}% - No audio detected")
    
    @pyqtSlot(str)
    def on_status_updated(self, status):
        """Update status display"""
        self.status_label.setText(f"Status: {status}")
    
    @pyqtSlot(str)
    def on_error(self, error_message):
        """Handle errors"""
        self.add_conversation_message("❌ Error", error_message, "#F44336")
        self.status_label.setText(f"Status: Error - {error_message[:50]}...")
    
    def add_conversation_message(self, sender: str, message: str, color: str):
        """Add message to conversation area"""
        timestamp = time.strftime("%H:%M:%S")
        
        formatted_message = f"""
        <div style="margin: 10px 0; padding: 15px; border-left: 5px solid {color}; 
                    background-color: #f9f9f9; border-radius: 10px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <strong style="color: {color}; font-size: 13px;">[{timestamp}] {sender}:</strong><br>
            <span style="margin-left: 10px; color: #333; font-size: 12px; line-height: 1.4;">{message}</span>
        </div>
        """
        
        self.conversation_area.append(formatted_message)
        
        # Auto-scroll to bottom
        cursor = self.conversation_area.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.conversation_area.setTextCursor(cursor)
    
    def closeEvent(self, event):
        """Handle window close"""
        if self.is_listening:
            self.stop_listening()
        
        if self.ollama_ai:
            self.ollama_ai.stop()
            self.ollama_ai.wait()
        
        event.accept()

def main():
    """Main entry point"""
    app = QApplication(sys.argv)
    
    window = DesktopAudioListenerWindow()
    window.show()
    
    # Welcome messages
    window.add_conversation_message("System", "🎉 Desktop Audio Listener Ready!", "#4CAF50")
    window.add_conversation_message("System", "This app listens to your desktop audio output (YouTube, music, etc.)", "#2196F3")
    window.add_conversation_message("System", "Make sure 'Stereo Mix' is enabled in Windows sound settings", "#FF9800")
    window.add_conversation_message("System", "Click 'Start Listening' and play some videos or music!", "#9C27B0")
    
    return app.exec()

if __name__ == "__main__":
    print("🖥️ Starting Desktop Audio Listener...")
    sys.exit(main())
