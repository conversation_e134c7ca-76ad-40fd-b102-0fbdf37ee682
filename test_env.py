"""
Quick test to check .env file loading
"""
import os
from dotenv import load_dotenv

print("🔍 Testing .env file loading...")

# Load .env file
load_dotenv()

# Check if API key is loaded
api_key = os.getenv('OPENAI_API_KEY')
print(f"API Key loaded: {api_key[:20]}..." if api_key else "❌ No API key found")

# Check all environment variables
print("\nAll environment variables from .env:")
for key, value in os.environ.items():
    if 'OPENAI' in key:
        print(f"{key}: {value[:20]}..." if value else f"{key}: (empty)")

# Test OpenAI connection
if api_key and api_key != 'your_openai_api_key_here':
    print("\n🤖 Testing OpenAI connection...")
    try:
        import openai
        client = openai.OpenAI(api_key=api_key)
        
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": "Say 'Hello' in one word"}],
            max_tokens=5
        )
        
        print(f"✅ OpenAI API test successful!")
        print(f"Response: {response.choices[0].message.content}")
        
    except Exception as e:
        print(f"❌ OpenAI API test failed: {e}")
else:
    print("❌ API key not properly loaded")
