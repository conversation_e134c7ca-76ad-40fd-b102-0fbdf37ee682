"""
Simple chat demo that works with what we have
"""
import sys
import time
import random
from PyQt6.QtWidgets import <PERSON>A<PERSON><PERSON>, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QTextEdit, QLabel
from PyQt6.QtCore import Qt

class SimpleChatWindow(QMainWindow):
    """Simple chat window for testing"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🤖 Simple AI Chat Demo")
        self.setGeometry(100, 100, 700, 500)
        
        self.tts_engine = None
        self.tts_enabled = False
        self.setup_ui()
        self.setup_tts()
        
        # Simple AI responses
        self.responses = [
            "That's really interesting! Tell me more about that.",
            "I understand how you feel. That sounds important to you.",
            "That's a great point. What made you think about that?",
            "I can see why that would matter to you. How does it make you feel?",
            "That's fascinating! I'd love to hear more details.",
            "I hear you. That sounds like something worth exploring.",
            "That's a thoughtful observation. What's your take on it?",
            "I appreciate you sharing that with me. What happens next?",
            "That sounds meaningful. Can you tell me more about your experience?",
            "I'm really interested in your perspective on this."
        ]
    
    def setup_ui(self):
        """Setup the user interface"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Title
        title = QLabel("🤖 Simple AI Chat Demo")
        title.setStyleSheet("font-size: 18px; font-weight: bold; padding: 10px; text-align: center;")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # Info
        info = QLabel("This demo shows the UI working while we wait for Ollama model to download")
        info.setStyleSheet("padding: 5px; background-color: #e8f4fd; border-radius: 5px;")
        info.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(info)
        
        # Conversation area
        self.conversation_area = QTextEdit()
        self.conversation_area.setReadOnly(True)
        self.conversation_area.setStyleSheet("""
            QTextEdit {
                font-size: 12px; 
                padding: 10px; 
                background-color: #fafafa;
                border: 1px solid #ddd;
                border-radius: 5px;
            }
        """)
        layout.addWidget(self.conversation_area)
        
        # Input area
        input_layout = QHBoxLayout()
        
        self.input_field = QTextEdit()
        self.input_field.setMaximumHeight(80)
        self.input_field.setPlaceholderText("Type your message here and press Enter...")
        self.input_field.setStyleSheet("""
            QTextEdit {
                font-size: 12px;
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 5px;
            }
            QTextEdit:focus {
                border-color: #4CAF50;
            }
        """)
        input_layout.addWidget(self.input_field)
        
        self.send_button = QPushButton("Send")
        self.send_button.setMinimumHeight(60)
        self.send_button.setStyleSheet("""
            QPushButton {
                font-size: 14px;
                font-weight: bold;
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)
        self.send_button.clicked.connect(self.send_message)
        input_layout.addWidget(self.send_button)
        
        layout.addLayout(input_layout)
        
        # Controls
        controls_layout = QHBoxLayout()
        
        self.tts_button = QPushButton("🔊 Enable TTS")
        self.tts_button.clicked.connect(self.toggle_tts)
        self.tts_button.setStyleSheet("""
            QPushButton {
                padding: 8px 16px;
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        controls_layout.addWidget(self.tts_button)
        
        self.clear_button = QPushButton("Clear Chat")
        self.clear_button.clicked.connect(self.clear_conversation)
        self.clear_button.setStyleSheet("""
            QPushButton {
                padding: 8px 16px;
                background-color: #FF9800;
                color: white;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """)
        controls_layout.addWidget(self.clear_button)
        
        layout.addLayout(controls_layout)
        
        # Connect Enter key to send
        self.input_field.keyPressEvent = self.input_key_press
    
    def setup_tts(self):
        """Setup text-to-speech"""
        try:
            import pyttsx3
            self.tts_engine = pyttsx3.init()
            print("✅ TTS engine initialized")
        except Exception as e:
            print(f"❌ TTS setup failed: {e}")
            self.tts_engine = None
    
    def input_key_press(self, event):
        """Handle key press in input field"""
        if event.key() == Qt.Key.Key_Return and not event.modifiers() & Qt.KeyboardModifier.ShiftModifier:
            self.send_message()
        else:
            QTextEdit.keyPressEvent(self.input_field, event)
    
    def send_message(self):
        """Send message to AI"""
        user_input = self.input_field.toPlainText().strip()
        if not user_input:
            return
        
        # Clear input
        self.input_field.clear()
        
        # Add user message
        self.add_message("You", user_input, "#0066cc")
        
        # Generate AI response (simulate thinking)
        self.add_message("System", "🤔 Thinking...", "#999999")
        
        # Simulate processing delay
        QApplication.processEvents()
        time.sleep(0.5)
        
        # Remove thinking message and add AI response
        self.conversation_area.undo()  # Remove the "thinking" message
        
        # Generate response
        ai_response = random.choice(self.responses)
        self.add_message("AI", ai_response, "#009900")
        
        # Speak response if TTS is enabled
        if self.tts_enabled and self.tts_engine:
            try:
                self.tts_engine.say(ai_response)
                self.tts_engine.runAndWait()
            except Exception as e:
                print(f"TTS error: {e}")
    
    def add_message(self, sender: str, message: str, color: str):
        """Add message to conversation"""
        timestamp = time.strftime("%H:%M:%S")
        
        # Choose emoji based on sender
        emoji = "👤" if sender == "You" else "🤖" if sender == "AI" else "ℹ️"
        
        formatted_message = f"""
        <div style="margin: 8px 0; padding: 12px; border-left: 4px solid {color}; background-color: #f9f9f9; border-radius: 5px;">
            <strong style="color: {color};">{emoji} [{timestamp}] {sender}:</strong><br>
            <span style="margin-left: 10px; color: #333;">{message}</span>
        </div>
        """
        
        self.conversation_area.append(formatted_message)
        
        # Auto-scroll to bottom
        cursor = self.conversation_area.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.conversation_area.setTextCursor(cursor)
    
    def toggle_tts(self):
        """Toggle text-to-speech"""
        if self.tts_engine:
            self.tts_enabled = not self.tts_enabled
            if self.tts_enabled:
                self.tts_button.setText("🔇 Disable TTS")
                self.add_message("System", "🔊 Text-to-Speech enabled", "#4CAF50")
            else:
                self.tts_button.setText("🔊 Enable TTS")
                self.add_message("System", "🔇 Text-to-Speech disabled", "#FF5722")
        else:
            self.add_message("System", "❌ TTS not available", "#F44336")
    
    def clear_conversation(self):
        """Clear conversation"""
        self.conversation_area.clear()
        self.add_message("System", "🧹 Conversation cleared", "#9C27B0")

def main():
    """Main application entry point"""
    app = QApplication(sys.argv)
    
    # Create and show window
    window = SimpleChatWindow()
    window.show()
    
    # Add welcome messages
    window.add_message("System", "🎉 Welcome to the Simple AI Chat Demo!", "#4CAF50")
    window.add_message("System", "This shows the UI working while Ollama downloads in the background.", "#2196F3")
    window.add_message("System", "Try typing a message below! The AI will respond with empathetic replies.", "#FF9800")
    window.add_message("AI", "Hello! I'm here to chat with you. What's on your mind today?", "#009900")
    
    return app.exec()

if __name__ == "__main__":
    print("🚀 Starting Simple Chat Demo...")
    print("This demonstrates the UI while Ollama model downloads")
    sys.exit(main())
