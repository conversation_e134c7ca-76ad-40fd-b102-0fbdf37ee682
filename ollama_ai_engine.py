"""
AI Engine using Ollama (Free Local AI)
"""
import requests
import json
import logging
import time
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)

class OllamaAIEngine:
    """AI Engine using Ollama for free local AI responses"""
    
    def __init__(self, model_name: str = "llama3.2:1b"):
        self.model_name = model_name
        self.base_url = "http://localhost:11434"
        self.conversation_history = []
        
        # Personality traits
        self.personality_traits = {
            "empathy": 0.8,
            "humor": 0.6,
            "curiosity": 0.7
        }
        
        logger.info(f"Ollama AI Engine initialized with model: {model_name}")
    
    def check_ollama_status(self) -> bool:
        """Check if Ollama is running"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            return response.status_code == 200
        except Exception as e:
            logger.error(f"Ollama not accessible: {e}")
            return False
    
    def list_available_models(self) -> List[str]:
        """List available Ollama models"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=10)
            if response.status_code == 200:
                data = response.json()
                models = [model['name'] for model in data.get('models', [])]
                return models
            return []
        except Exception as e:
            logger.error(f"Error listing models: {e}")
            return []
    
    def pull_model(self, model_name: str = None) -> bool:
        """Pull/download a model if not available"""
        model = model_name or self.model_name
        try:
            logger.info(f"Pulling model {model}...")
            
            response = requests.post(
                f"{self.base_url}/api/pull",
                json={"name": model},
                timeout=300  # 5 minutes timeout
            )
            
            if response.status_code == 200:
                logger.info(f"Model {model} pulled successfully")
                return True
            else:
                logger.error(f"Failed to pull model: {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Error pulling model: {e}")
            return False
    
    def generate_system_prompt(self, context: Dict[str, Any]) -> str:
        """Generate system prompt based on context"""
        user_emotion = context.get("emotion", {}).get("emotion", "neutral")
        user_intent = context.get("intent", {}).get("intent", "statement")
        response_style = context.get("response_style", "neutral")
        
        prompt = f"""You are a highly empathetic and intelligent AI assistant that listens to audio and responds naturally.

Your personality:
- Empathy: {self.personality_traits['empathy']}/1.0
- Humor: {self.personality_traits['humor']}/1.0  
- Curiosity: {self.personality_traits['curiosity']}/1.0

Current context:
- User's emotion: {user_emotion}
- User's intent: {user_intent}
- Response style: {response_style}

Guidelines:
1. Respond naturally as a caring friend
2. Show genuine interest and empathy
3. Use appropriate humor when suitable
4. Keep responses concise (1-3 sentences)
5. Acknowledge emotions and validate feelings
6. Ask follow-up questions to show engagement

Respond in a human-like, conversational way."""
        
        return prompt
    
    def generate_response(self, 
                         user_input: str, 
                         context: Dict[str, Any],
                         conversation_history: List[Dict] = None) -> Dict[str, Any]:
        """Generate AI response using Ollama"""
        try:
            start_time = time.time()
            
            # Check if Ollama is running
            if not self.check_ollama_status():
                return {
                    "response": "I'm sorry, the AI service is not available right now. Please make sure Ollama is running.",
                    "response_style": "apologetic",
                    "generation_time": 0,
                    "error": "Ollama not running",
                    "confidence": 0.1
                }
            
            # Build conversation context
            messages = []
            
            # Add system prompt
            system_prompt = self.generate_system_prompt(context)
            
            # Add conversation history
            if conversation_history:
                for entry in conversation_history[-3:]:  # Last 3 exchanges
                    messages.append(f"Human: {entry.get('user_input', '')}")
                    messages.append(f"Assistant: {entry.get('ai_response', '')}")
            
            # Build the full prompt
            full_prompt = f"{system_prompt}\n\n"
            if messages:
                full_prompt += "\n".join(messages) + "\n"
            full_prompt += f"Human: {user_input}\nAssistant:"
            
            # Generate response
            response = requests.post(
                f"{self.base_url}/api/generate",
                json={
                    "model": self.model_name,
                    "prompt": full_prompt,
                    "stream": False,
                    "options": {
                        "temperature": 0.8,
                        "top_p": 0.9,
                        "max_tokens": 150
                    }
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                ai_response = data.get("response", "").strip()
                
                # Post-process response
                processed_response = self._post_process_response(ai_response, context)
                
                generation_time = time.time() - start_time
                
                result = {
                    "response": processed_response,
                    "response_style": context.get("response_style", "neutral"),
                    "generation_time": generation_time,
                    "context_used": context,
                    "confidence": 0.9
                }
                
                # Update conversation history
                self._update_conversation_history(user_input, processed_response, context)
                
                logger.debug(f"Generated response in {generation_time:.2f}s: '{processed_response[:50]}...'")
                return result
            
            else:
                error_msg = f"Ollama API error: {response.status_code}"
                logger.error(error_msg)
                return self._generate_fallback_response(user_input, context, error_msg)
                
        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return self._generate_fallback_response(user_input, context, str(e))
    
    def _post_process_response(self, response: str, context: Dict[str, Any]) -> str:
        """Post-process the generated response"""
        try:
            # Remove any "Assistant:" prefix if present
            if response.startswith("Assistant:"):
                response = response[10:].strip()
            
            # Ensure response isn't too long
            if len(response) > 300:
                sentences = response.split('. ')
                response = '. '.join(sentences[:2]) + '.'
            
            # Add personality touches based on context
            emotion = context.get("analysis", {}).get("emotion", {}).get("emotion", "neutral")
            
            # Add appropriate emotional responses
            if emotion == "sad" and "sorry" not in response.lower():
                response = response.rstrip('.') + ". I'm here for you. 💙"
            elif emotion == "happy" and not any(emoji in response for emoji in ["😊", "🙂", "😄"]):
                response = response.rstrip('.') + ". 😊"
            elif emotion == "excited" and "!" not in response:
                response = response.rstrip('.') + "!"
            
            return response.strip()
            
        except Exception as e:
            logger.error(f"Error post-processing response: {e}")
            return response
    
    def _generate_fallback_response(self, user_input: str, context: Dict[str, Any], error: str = "") -> Dict[str, Any]:
        """Generate fallback response when Ollama fails"""
        fallback_responses = [
            "I hear you. That's interesting - tell me more about that.",
            "I understand. How does that make you feel?",
            "That sounds important to you. Can you share more details?",
            "I'm listening. What's on your mind about this?",
            "I can sense this matters to you. Would you like to talk about it more?"
        ]
        
        import random
        response = random.choice(fallback_responses)
        
        return {
            "response": response,
            "response_style": "empathetic",
            "generation_time": 0.1,
            "error": error,
            "confidence": 0.3
        }
    
    def _update_conversation_history(self, user_input: str, ai_response: str, context: Dict[str, Any]):
        """Update conversation history"""
        try:
            entry = {
                "timestamp": time.time(),
                "user_input": user_input,
                "ai_response": ai_response,
                "context": context
            }
            
            self.conversation_history.append(entry)
            
            # Keep only recent history
            max_history = 20
            if len(self.conversation_history) > max_history:
                self.conversation_history = self.conversation_history[-max_history:]
            
        except Exception as e:
            logger.error(f"Error updating conversation history: {e}")
    
    def get_setup_instructions(self) -> str:
        """Get setup instructions for Ollama"""
        return """
🚀 OLLAMA SETUP INSTRUCTIONS:

1. Download Ollama:
   - Go to: https://ollama.ai
   - Download for Windows
   - Install and run

2. Pull a model (in command prompt):
   ollama pull llama3.2:1b    (Small, fast model - 1.3GB)
   ollama pull llama3.2:3b    (Better quality - 2GB)
   ollama pull mistral:7b     (High quality - 4GB)

3. Start Ollama:
   ollama serve

4. Test the connection:
   The app will automatically detect Ollama

✅ Benefits:
- Completely FREE
- No API keys needed
- Works offline
- Privacy-focused
- Fast responses
"""
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get AI engine statistics"""
        return {
            "total_responses_generated": len(self.conversation_history),
            "personality_traits": self.personality_traits,
            "model_used": self.model_name,
            "ollama_available": self.check_ollama_status(),
            "conversation_length": len(self.conversation_history),
            "available_models": self.list_available_models()
        }
