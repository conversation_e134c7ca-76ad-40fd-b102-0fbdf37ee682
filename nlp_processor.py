"""
Natural Language Processing module for sentiment analysis and context understanding
"""
import re
import logging
import time
from typing import Dict, List, Any, Tuple, Optional
import numpy as np
from transformers import pipeline, AutoTokenizer, AutoModelForSequenceClassification
import torch
from config import Config, PersonalityConfig

logger = logging.getLogger(__name__)

class NLPProcessor:
    """Processes text for sentiment, emotion, intent, and context"""
    
    def __init__(self):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        # Initialize models
        self.sentiment_analyzer = None
        self.emotion_analyzer = None
        self.tokenizer = None
        
        # Context tracking
        self.conversation_context = []
        self.user_profile = {
            "preferences": {},
            "emotional_patterns": {},
            "topics_of_interest": [],
            "communication_style": "neutral"
        }
        
        # Keywords and patterns
        self.emotion_keywords = {
            "happy": ["happy", "joy", "excited", "great", "awesome", "wonderful", "amazing", "love", "fantastic"],
            "sad": ["sad", "depressed", "down", "upset", "disappointed", "hurt", "crying", "terrible"],
            "angry": ["angry", "mad", "furious", "annoyed", "frustrated", "irritated", "hate", "stupid"],
            "anxious": ["worried", "nervous", "anxious", "scared", "afraid", "concerned", "stress", "panic"],
            "confused": ["confused", "lost", "unclear", "don't understand", "what", "how", "why"],
            "excited": ["excited", "thrilled", "pumped", "can't wait", "amazing", "incredible"]
        }
        
        self.intent_patterns = {
            "question": [r"\?", r"^(what|how|when|where|why|who)", r"(can you|could you|would you)"],
            "request": [r"(please|can you|could you|would you)", r"(help|assist|support)"],
            "complaint": [r"(problem|issue|wrong|broken|doesn't work)", r"(hate|dislike|terrible)"],
            "compliment": [r"(good|great|excellent|amazing|wonderful)", r"(thank|thanks|appreciate)"],
            "greeting": [r"^(hi|hello|hey|good morning|good afternoon|good evening)"],
            "goodbye": [r"(bye|goodbye|see you|talk later|gotta go)"]
        }
        
        logger.info(f"NLP Processor initialized on device: {self.device}")
    
    def load_models(self):
        """Load NLP models"""
        try:
            # Load sentiment analysis model
            if self.sentiment_analyzer is None:
                logger.info("Loading sentiment analysis model...")
                self.sentiment_analyzer = pipeline(
                    "sentiment-analysis",
                    model="cardiffnlp/twitter-roberta-base-sentiment-latest",
                    device=0 if self.device == "cuda" else -1
                )
            
            # Load emotion analysis model
            if self.emotion_analyzer is None:
                logger.info("Loading emotion analysis model...")
                self.emotion_analyzer = pipeline(
                    "text-classification",
                    model="j-hartmann/emotion-english-distilroberta-base",
                    device=0 if self.device == "cuda" else -1
                )
            
            logger.info("NLP models loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error loading NLP models: {e}")
            return False
    
    def analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """Analyze sentiment of text"""
        try:
            if not text.strip():
                return {"label": "neutral", "score": 0.5}
            
            if self.sentiment_analyzer is None:
                if not self.load_models():
                    return self._fallback_sentiment_analysis(text)
            
            result = self.sentiment_analyzer(text)[0]
            
            # Normalize labels
            label_mapping = {
                "LABEL_0": "negative",
                "LABEL_1": "neutral", 
                "LABEL_2": "positive",
                "NEGATIVE": "negative",
                "NEUTRAL": "neutral",
                "POSITIVE": "positive"
            }
            
            normalized_label = label_mapping.get(result["label"], result["label"].lower())
            
            return {
                "label": normalized_label,
                "score": result["score"],
                "confidence": result["score"]
            }
            
        except Exception as e:
            logger.error(f"Error analyzing sentiment: {e}")
            return self._fallback_sentiment_analysis(text)
    
    def analyze_emotion(self, text: str) -> Dict[str, Any]:
        """Analyze emotion in text"""
        try:
            if not text.strip():
                return {"emotion": "neutral", "score": 0.5}
            
            if self.emotion_analyzer is None:
                if not self.load_models():
                    return self._fallback_emotion_analysis(text)
            
            result = self.emotion_analyzer(text)[0]
            
            return {
                "emotion": result["label"].lower(),
                "score": result["score"],
                "confidence": result["score"]
            }
            
        except Exception as e:
            logger.error(f"Error analyzing emotion: {e}")
            return self._fallback_emotion_analysis(text)
    
    def _fallback_sentiment_analysis(self, text: str) -> Dict[str, Any]:
        """Fallback sentiment analysis using keywords"""
        text_lower = text.lower()
        
        positive_words = ["good", "great", "excellent", "amazing", "wonderful", "love", "like", "happy", "joy"]
        negative_words = ["bad", "terrible", "awful", "hate", "dislike", "sad", "angry", "frustrated"]
        
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        
        if positive_count > negative_count:
            return {"label": "positive", "score": 0.7}
        elif negative_count > positive_count:
            return {"label": "negative", "score": 0.7}
        else:
            return {"label": "neutral", "score": 0.5}
    
    def _fallback_emotion_analysis(self, text: str) -> Dict[str, Any]:
        """Fallback emotion analysis using keywords"""
        text_lower = text.lower()
        
        emotion_scores = {}
        for emotion, keywords in self.emotion_keywords.items():
            score = sum(1 for keyword in keywords if keyword in text_lower)
            if score > 0:
                emotion_scores[emotion] = score
        
        if emotion_scores:
            dominant_emotion = max(emotion_scores, key=emotion_scores.get)
            confidence = min(0.8, emotion_scores[dominant_emotion] * 0.2)
            return {"emotion": dominant_emotion, "score": confidence}
        
        return {"emotion": "neutral", "score": 0.5}
    
    def detect_intent(self, text: str) -> Dict[str, Any]:
        """Detect user intent from text"""
        try:
            text_lower = text.lower().strip()
            
            intent_scores = {}
            
            for intent, patterns in self.intent_patterns.items():
                score = 0
                for pattern in patterns:
                    if re.search(pattern, text_lower):
                        score += 1
                
                if score > 0:
                    intent_scores[intent] = score
            
            if intent_scores:
                primary_intent = max(intent_scores, key=intent_scores.get)
                confidence = min(0.9, intent_scores[primary_intent] * 0.3)
                
                return {
                    "intent": primary_intent,
                    "confidence": confidence,
                    "all_intents": intent_scores
                }
            
            return {"intent": "statement", "confidence": 0.5, "all_intents": {}}
            
        except Exception as e:
            logger.error(f"Error detecting intent: {e}")
            return {"intent": "unknown", "confidence": 0.0, "all_intents": {}}
    
    def extract_topics(self, text: str) -> List[str]:
        """Extract topics/keywords from text"""
        try:
            # Simple keyword extraction
            words = re.findall(r'\b[a-zA-Z]{3,}\b', text.lower())
            
            # Filter out common words
            stop_words = {
                "the", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by",
                "from", "up", "about", "into", "through", "during", "before", "after", "above",
                "below", "between", "among", "this", "that", "these", "those", "i", "you", "he",
                "she", "it", "we", "they", "me", "him", "her", "us", "them", "my", "your", "his",
                "her", "its", "our", "their", "am", "is", "are", "was", "were", "be", "been",
                "being", "have", "has", "had", "do", "does", "did", "will", "would", "could",
                "should", "may", "might", "must", "can", "shall"
            }
            
            topics = [word for word in words if word not in stop_words and len(word) > 3]
            
            # Count frequency and return most common
            topic_counts = {}
            for topic in topics:
                topic_counts[topic] = topic_counts.get(topic, 0) + 1
            
            # Return topics sorted by frequency
            sorted_topics = sorted(topic_counts.items(), key=lambda x: x[1], reverse=True)
            return [topic for topic, count in sorted_topics[:5]]
            
        except Exception as e:
            logger.error(f"Error extracting topics: {e}")
            return []
    
    def analyze_text_comprehensive(self, text: str) -> Dict[str, Any]:
        """Comprehensive text analysis"""
        try:
            # Basic analysis
            sentiment = self.analyze_sentiment(text)
            emotion = self.analyze_emotion(text)
            intent = self.detect_intent(text)
            topics = self.extract_topics(text)
            
            # Text statistics
            word_count = len(text.split())
            char_count = len(text)
            sentence_count = len(re.split(r'[.!?]+', text))
            
            # Complexity analysis
            avg_word_length = np.mean([len(word) for word in text.split()]) if text.split() else 0
            
            return {
                "sentiment": sentiment,
                "emotion": emotion,
                "intent": intent,
                "topics": topics,
                "statistics": {
                    "word_count": word_count,
                    "char_count": char_count,
                    "sentence_count": sentence_count,
                    "avg_word_length": avg_word_length
                },
                "text": text
            }
            
        except Exception as e:
            logger.error(f"Error in comprehensive text analysis: {e}")
            return {"error": str(e)}
    
    def update_context(self, text: str, analysis: Dict[str, Any]):
        """Update conversation context"""
        try:
            context_entry = {
                "timestamp": time.time(),
                "text": text,
                "analysis": analysis
            }
            
            self.conversation_context.append(context_entry)
            
            # Keep only recent context
            max_context = Config.MAX_CONTEXT_HISTORY
            if len(self.conversation_context) > max_context:
                self.conversation_context = self.conversation_context[-max_context:]
            
            # Update user profile
            self._update_user_profile(analysis)
            
        except Exception as e:
            logger.error(f"Error updating context: {e}")
    
    def _update_user_profile(self, analysis: Dict[str, Any]):
        """Update user profile based on analysis"""
        try:
            # Update emotional patterns
            emotion = analysis.get("emotion", {}).get("emotion", "neutral")
            if emotion in self.user_profile["emotional_patterns"]:
                self.user_profile["emotional_patterns"][emotion] += 1
            else:
                self.user_profile["emotional_patterns"][emotion] = 1
            
            # Update topics of interest
            topics = analysis.get("topics", [])
            for topic in topics:
                if topic not in self.user_profile["topics_of_interest"]:
                    self.user_profile["topics_of_interest"].append(topic)
            
            # Keep only top topics
            if len(self.user_profile["topics_of_interest"]) > 20:
                self.user_profile["topics_of_interest"] = self.user_profile["topics_of_interest"][:20]
            
        except Exception as e:
            logger.error(f"Error updating user profile: {e}")
    
    def get_context_summary(self) -> Dict[str, Any]:
        """Get summary of conversation context"""
        try:
            if not self.conversation_context:
                return {"summary": "No conversation context available"}
            
            # Analyze recent emotions
            recent_emotions = [
                entry["analysis"].get("emotion", {}).get("emotion", "neutral")
                for entry in self.conversation_context[-5:]
            ]
            
            # Analyze recent topics
            recent_topics = []
            for entry in self.conversation_context[-5:]:
                recent_topics.extend(entry["analysis"].get("topics", []))
            
            # Most common recent emotion
            emotion_counts = {}
            for emotion in recent_emotions:
                emotion_counts[emotion] = emotion_counts.get(emotion, 0) + 1
            
            dominant_emotion = max(emotion_counts, key=emotion_counts.get) if emotion_counts else "neutral"
            
            return {
                "conversation_length": len(self.conversation_context),
                "dominant_emotion": dominant_emotion,
                "recent_topics": list(set(recent_topics))[:5],
                "user_profile": self.user_profile,
                "context_available": True
            }
            
        except Exception as e:
            logger.error(f"Error getting context summary: {e}")
            return {"error": str(e)}
    
    def get_response_style_recommendation(self, analysis: Dict[str, Any]) -> str:
        """Recommend response style based on analysis"""
        try:
            emotion = analysis.get("emotion", {}).get("emotion", "neutral")
            sentiment = analysis.get("sentiment", {}).get("label", "neutral")
            intent = analysis.get("intent", {}).get("intent", "statement")
            
            # Map emotions to response styles
            if emotion in ["sad", "anxious"]:
                return "empathetic"
            elif emotion in ["happy", "excited"]:
                return "enthusiastic"
            elif emotion == "angry":
                return "calming"
            elif intent == "question":
                return "informative"
            elif intent == "complaint":
                return "supportive"
            elif sentiment == "positive":
                return "positive"
            else:
                return "neutral"
                
        except Exception as e:
            logger.error(f"Error getting response style recommendation: {e}")
            return "neutral"
