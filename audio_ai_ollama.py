"""
Audio AI Application using Ollama (Free Local AI)
"""
import sys
import os
import logging
import time
import threading
import requests
import json
from pathlib import Path
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QTextEdit, QLabel
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QThread, pyqtSlot

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OllamaWorker(QThread):
    """Worker thread for Ollama AI processing"""
    response_ready = pyqtSignal(str, str)  # user_text, ai_response
    error_occurred = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.model_name = "llama3.2:1b"
        self.pending_requests = []
        self.running = True
    
    def add_request(self, user_input: str):
        """Add a request to process"""
        self.pending_requests.append(user_input)
    
    def run(self):
        """Main worker loop"""
        while self.running:
            if self.pending_requests:
                user_input = self.pending_requests.pop(0)
                self.process_request(user_input)
            else:
                self.msleep(100)  # Sleep for 100ms
    
    def process_request(self, user_input: str):
        """Process a single request"""
        try:
            # Generate AI response
            response = requests.post(
                "http://localhost:11434/api/generate",
                json={
                    "model": self.model_name,
                    "prompt": f"You are a helpful, empathetic AI assistant. Respond naturally and conversationally in 1-2 sentences to: {user_input}",
                    "stream": False,
                    "options": {
                        "temperature": 0.8,
                        "max_tokens": 100
                    }
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                ai_response = data.get("response", "").strip()
                
                # Clean up response
                if ai_response.startswith("Assistant:"):
                    ai_response = ai_response[10:].strip()
                
                self.response_ready.emit(user_input, ai_response)
            else:
                self.error_occurred.emit(f"Ollama API error: {response.status_code}")
                
        except Exception as e:
            self.error_occurred.emit(f"Error generating response: {e}")
    
    def stop(self):
        """Stop the worker"""
        self.running = False

class AudioAIWindow(QMainWindow):
    """Main application window"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Audio AI with Ollama (Free Local AI)")
        self.setGeometry(100, 100, 800, 600)
        
        # Initialize components
        self.ollama_worker = None
        self.tts_engine = None
        self.setup_ui()
        self.setup_ollama()
        self.setup_tts()
        
        logger.info("Audio AI application initialized")
    
    def setup_ui(self):
        """Setup the user interface"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Title
        title = QLabel("🎤 Audio AI with Ollama (Free Local AI)")
        title.setStyleSheet("font-size: 18px; font-weight: bold; padding: 10px;")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # Status
        self.status_label = QLabel("Status: Initializing...")
        self.status_label.setStyleSheet("padding: 5px; background-color: #f0f0f0;")
        layout.addWidget(self.status_label)
        
        # Conversation area
        self.conversation_area = QTextEdit()
        self.conversation_area.setReadOnly(True)
        self.conversation_area.setStyleSheet("font-size: 12px; padding: 10px;")
        layout.addWidget(self.conversation_area)
        
        # Input area
        input_layout = QHBoxLayout()
        
        self.input_field = QTextEdit()
        self.input_field.setMaximumHeight(100)
        self.input_field.setPlaceholderText("Type your message here...")
        input_layout.addWidget(self.input_field)
        
        self.send_button = QPushButton("Send")
        self.send_button.setMinimumHeight(50)
        self.send_button.clicked.connect(self.send_message)
        input_layout.addWidget(self.send_button)
        
        layout.addLayout(input_layout)
        
        # Controls
        controls_layout = QHBoxLayout()
        
        self.tts_button = QPushButton("🔊 Enable TTS")
        self.tts_button.clicked.connect(self.toggle_tts)
        controls_layout.addWidget(self.tts_button)
        
        self.clear_button = QPushButton("Clear Chat")
        self.clear_button.clicked.connect(self.clear_conversation)
        controls_layout.addWidget(self.clear_button)
        
        self.test_button = QPushButton("Test Ollama")
        self.test_button.clicked.connect(self.test_ollama)
        controls_layout.addWidget(self.test_button)
        
        layout.addLayout(controls_layout)
        
        # Connect Enter key to send
        self.input_field.keyPressEvent = self.input_key_press
    
    def setup_ollama(self):
        """Setup Ollama worker"""
        try:
            self.ollama_worker = OllamaWorker()
            self.ollama_worker.response_ready.connect(self.on_response_ready)
            self.ollama_worker.error_occurred.connect(self.on_error)
            self.ollama_worker.start()
            
            self.status_label.setText("Status: Ollama worker started")
            
        except Exception as e:
            logger.error(f"Error setting up Ollama: {e}")
            self.status_label.setText(f"Status: Ollama setup failed - {e}")
    
    def setup_tts(self):
        """Setup text-to-speech"""
        try:
            import pyttsx3
            self.tts_engine = pyttsx3.init()
            self.tts_enabled = False
            logger.info("TTS engine initialized")
        except Exception as e:
            logger.error(f"TTS setup failed: {e}")
            self.tts_engine = None
    
    def input_key_press(self, event):
        """Handle key press in input field"""
        if event.key() == Qt.Key.Key_Return and not event.modifiers() & Qt.KeyboardModifier.ShiftModifier:
            self.send_message()
        else:
            QTextEdit.keyPressEvent(self.input_field, event)
    
    def send_message(self):
        """Send message to AI"""
        user_input = self.input_field.toPlainText().strip()
        if not user_input:
            return
        
        # Clear input
        self.input_field.clear()
        
        # Add to conversation
        self.add_message("You", user_input)
        
        # Send to Ollama worker
        if self.ollama_worker:
            self.ollama_worker.add_request(user_input)
            self.status_label.setText("Status: Thinking...")
        else:
            self.add_message("AI", "Sorry, Ollama is not available.")
    
    @pyqtSlot(str, str)
    def on_response_ready(self, user_text, ai_response):
        """Handle AI response"""
        self.add_message("AI", ai_response)
        self.status_label.setText("Status: Ready")
        
        # Speak response if TTS is enabled
        if self.tts_enabled and self.tts_engine:
            try:
                self.tts_engine.say(ai_response)
                self.tts_engine.runAndWait()
            except Exception as e:
                logger.error(f"TTS error: {e}")
    
    @pyqtSlot(str)
    def on_error(self, error_message):
        """Handle error"""
        self.add_message("System", f"Error: {error_message}")
        self.status_label.setText(f"Status: Error - {error_message}")
    
    def add_message(self, sender: str, message: str):
        """Add message to conversation"""
        timestamp = time.strftime("%H:%M:%S")
        
        if sender == "You":
            color = "#0066cc"
        elif sender == "AI":
            color = "#009900"
        else:
            color = "#cc6600"
        
        formatted_message = f"""
        <div style="margin: 10px 0; padding: 8px; border-left: 3px solid {color};">
            <strong style="color: {color};">[{timestamp}] {sender}:</strong><br>
            <span style="margin-left: 10px;">{message}</span>
        </div>
        """
        
        self.conversation_area.append(formatted_message)
        
        # Auto-scroll to bottom
        cursor = self.conversation_area.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.conversation_area.setTextCursor(cursor)
    
    def toggle_tts(self):
        """Toggle text-to-speech"""
        if self.tts_engine:
            self.tts_enabled = not self.tts_enabled
            if self.tts_enabled:
                self.tts_button.setText("🔇 Disable TTS")
                self.add_message("System", "Text-to-Speech enabled")
            else:
                self.tts_button.setText("🔊 Enable TTS")
                self.add_message("System", "Text-to-Speech disabled")
        else:
            self.add_message("System", "TTS not available")
    
    def clear_conversation(self):
        """Clear conversation"""
        self.conversation_area.clear()
        self.add_message("System", "Conversation cleared")
    
    def test_ollama(self):
        """Test Ollama connection"""
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            if response.status_code == 200:
                data = response.json()
                models = [model['name'] for model in data.get('models', [])]
                if models:
                    self.add_message("System", f"✅ Ollama is working! Available models: {', '.join(models)}")
                else:
                    self.add_message("System", "⚠️ Ollama is running but no models are installed. Run: ollama pull llama3.2:1b")
            else:
                self.add_message("System", f"❌ Ollama error: {response.status_code}")
        except Exception as e:
            self.add_message("System", f"❌ Cannot connect to Ollama: {e}")
    
    def closeEvent(self, event):
        """Handle window close"""
        if self.ollama_worker:
            self.ollama_worker.stop()
            self.ollama_worker.wait()
        event.accept()

def main():
    """Main application entry point"""
    app = QApplication(sys.argv)
    
    # Create and show window
    window = AudioAIWindow()
    window.show()
    
    # Add welcome message
    window.add_message("System", "🎉 Welcome to Audio AI with Ollama!")
    window.add_message("System", "This is a free, local AI that runs on your computer.")
    window.add_message("System", "Type a message below to start chatting!")
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
